2025-06-26 14:11:46,650 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-06-26 14:11:46,650 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-06-26 14:11:46,651 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-06-26 14:11:46,651 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-06-26 14:11:46,651 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-06-26 14:11:46,652 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-06-26 14:11:46,652 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-06-26 14:11:46,652 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-06-26 14:11:46,653 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-06-26 14:11:46,653 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-06-26 14:11:46,653 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-06-26 14:11:46,653 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
2025-06-26 14:11:46,653 - __main__ - INFO - Killing any existing Appium and iproxy processes...
2025-06-26 14:11:48,716 - __main__ - INFO - Existing processes terminated
2025-06-26 14:11:49,544 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-06-26 14:11:49,575 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-06-26 14:11:49,898 - app - INFO - Using directories from config.py:
2025-06-26 14:11:49,898 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
2025-06-26 14:11:49,898 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
2025-06-26 14:11:49,898 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-06-26 14:11:49,900] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-06-26 14:11:49,901] INFO in database: Test_steps table schema updated successfully
[2025-06-26 14:11:49,901] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-06-26 14:11:49,901] INFO in database: Screenshots table schema updated successfully
[2025-06-26 14:11:49,901] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-06-26 14:11:49,902] INFO in database: step_idx column already exists in execution_tracking table
[2025-06-26 14:11:49,902] INFO in database: action_type column already exists in execution_tracking table
[2025-06-26 14:11:49,902] INFO in database: action_params column already exists in execution_tracking table
[2025-06-26 14:11:49,902] INFO in database: action_id column already exists in execution_tracking table
[2025-06-26 14:11:49,903] INFO in database: Successfully updated execution_tracking table schema
[2025-06-26 14:11:49,903] INFO in database: Database initialized successfully
[2025-06-26 14:11:49,903] INFO in database: Checking initial database state...
[2025-06-26 14:11:49,904] INFO in database: Database state: 0 suites, 0 cases, 8381 steps, 1 screenshots, 12 tracking entries
[2025-06-26 14:11:49,905] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-06-26 14:11:49,905] INFO in database: Test_steps table schema updated successfully
[2025-06-26 14:11:49,906] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-06-26 14:11:49,906] INFO in database: Screenshots table schema updated successfully
[2025-06-26 14:11:49,906] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-06-26 14:11:49,906] INFO in database: step_idx column already exists in execution_tracking table
[2025-06-26 14:11:49,906] INFO in database: action_type column already exists in execution_tracking table
[2025-06-26 14:11:49,907] INFO in database: action_params column already exists in execution_tracking table
[2025-06-26 14:11:49,907] INFO in database: action_id column already exists in execution_tracking table
[2025-06-26 14:11:49,907] INFO in database: Successfully updated execution_tracking table schema
[2025-06-26 14:11:49,907] INFO in database: Database initialized successfully
[2025-06-26 14:11:49,907] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-06-26 14:11:49,907] INFO in database: step_idx column already exists in execution_tracking table
[2025-06-26 14:11:49,907] INFO in database: action_type column already exists in execution_tracking table
[2025-06-26 14:11:49,907] INFO in database: action_params column already exists in execution_tracking table
[2025-06-26 14:11:49,907] INFO in database: action_id column already exists in execution_tracking table
[2025-06-26 14:11:49,908] INFO in database: Successfully updated execution_tracking table schema
[2025-06-26 14:11:49,908] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-06-26 14:11:49,908] INFO in database: Screenshots table schema updated successfully
[2025-06-26 14:11:49,908] INFO in database: === CLEARING EXECUTION TRACKING TABLE ===
[2025-06-26 14:11:49,909] INFO in database: Found 12 records in execution_tracking table before clearing
[2025-06-26 14:11:49,911] INFO in database: Successfully cleared execution_tracking table. Removed 12 records.
[2025-06-26 14:11:49,969] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
[2025-06-26 14:11:49,969] INFO in global_values_db: Global values database initialized successfully
[2025-06-26 14:11:49,970] INFO in global_values_db: Using global values from config.py
[2025-06-26 14:11:49,970] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300}
[2025-06-26 14:11:50,009] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-06-26 14:11:50,019] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1148c5a90>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-06-26 14:11:50,019] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[2025-06-26 14:11:50,056] INFO in appium_device_controller: Attempted to kill Appium processes
[2025-06-26 14:11:50,092] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
[2025-06-26 14:11:52,099] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[2025-06-26 14:11:52,099] INFO in appium_device_controller: Using local Appium installation at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium
[2025-06-26 14:11:52,849] INFO in appium_device_controller: Installed Appium drivers: 
[2025-06-26 14:11:52,849] INFO in appium_device_controller: Installing XCUITest driver...
[31mError: ✖ A driver named "xcuitest" is already installed. Did you mean to update? Run "appium driver update". See installed drivers with "appium driver list --installed".[39m
[2025-06-26 14:11:53,570] ERROR in appium_device_controller: Error checking/installing drivers: Command '['/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium', 'driver', 'install', 'xcuitest']' returned non-zero exit status 1.
[2025-06-26 14:11:53,570] INFO in appium_device_controller: Enabling inspector plugin if available
[2025-06-26 14:11:53,570] INFO in appium_device_controller: Appium server output will be logged to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/appium_server.log
[2025-06-26 14:11:53,576] INFO in appium_device_controller: Started Appium server using command: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium --base-path /wd/hub --port 4723 --relaxed-security --use-drivers xcuitest,uiautomator2 --use-plugins=inspector --session-override --allow-cors --allow-insecure chromedriver_autodownload
[2025-06-26 14:11:55,600] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 14:11:55,600] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '36da10ee7e9cb2a90558cad1d3933516476dc094', 'built': '2025-06-24 18:31:12 +1000'}}}
[2025-06-26 14:11:57,608] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 14:11:57,608] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '36da10ee7e9cb2a90558cad1d3933516476dc094', 'built': '2025-06-24 18:31:12 +1000'}}}
[2025-06-26 14:11:59,619] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 14:11:59,619] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '36da10ee7e9cb2a90558cad1d3933516476dc094', 'built': '2025-06-24 18:31:12 +1000'}}}
[2025-06-26 14:12:01,627] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 14:12:01,627] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '36da10ee7e9cb2a90558cad1d3933516476dc094', 'built': '2025-06-24 18:31:12 +1000'}}}
[2025-06-26 14:12:03,636] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 14:12:03,636] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '36da10ee7e9cb2a90558cad1d3933516476dc094', 'built': '2025-06-24 18:31:12 +1000'}}}
[2025-06-26 14:12:05,647] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 14:12:05,647] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '36da10ee7e9cb2a90558cad1d3933516476dc094', 'built': '2025-06-24 18:31:12 +1000'}}}
[2025-06-26 14:12:07,654] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 14:12:07,654] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '36da10ee7e9cb2a90558cad1d3933516476dc094', 'built': '2025-06-24 18:31:12 +1000'}}}
[2025-06-26 14:12:09,664] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 14:12:09,664] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '36da10ee7e9cb2a90558cad1d3933516476dc094', 'built': '2025-06-24 18:31:12 +1000'}}}
[2025-06-26 14:12:11,677] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 14:12:11,677] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '36da10ee7e9cb2a90558cad1d3933516476dc094', 'built': '2025-06-24 18:31:12 +1000'}}}
[2025-06-26 14:12:13,684] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 14:12:13,684] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '36da10ee7e9cb2a90558cad1d3933516476dc094', 'built': '2025-06-24 18:31:12 +1000'}}}
[2025-06-26 14:12:15,689] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 14:12:15,689] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '36da10ee7e9cb2a90558cad1d3933516476dc094', 'built': '2025-06-24 18:31:12 +1000'}}}
[2025-06-26 14:12:17,697] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 14:12:17,697] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '36da10ee7e9cb2a90558cad1d3933516476dc094', 'built': '2025-06-24 18:31:12 +1000'}}}
[2025-06-26 14:12:19,706] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 14:12:19,707] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '36da10ee7e9cb2a90558cad1d3933516476dc094', 'built': '2025-06-24 18:31:12 +1000'}}}
[2025-06-26 14:12:21,713] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 14:12:21,713] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '36da10ee7e9cb2a90558cad1d3933516476dc094', 'built': '2025-06-24 18:31:12 +1000'}}}
[2025-06-26 14:12:23,719] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 14:12:23,720] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '36da10ee7e9cb2a90558cad1d3933516476dc094', 'built': '2025-06-24 18:31:12 +1000'}}}
Starting Mobile App Automation Tool...
Configuration:
  - Flask server port: 8080
  - Appium server port: 4723
  - WebDriverAgent port: 8100
Open your web browser and navigate to: http://localhost:8080
 * Serving Flask app 'app'
 * Debug mode: on
[2025-06-26 14:12:23,745] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8080
 * Running on http://************:8080
[2025-06-26 14:12:23,745] INFO in _internal: [33mPress CTRL+C to quit[0m
[2025-06-26 14:12:27,194] INFO in directory_paths_db: Directory paths and environments database initialized/verified
[2025-06-26 14:12:27,195] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:12:27,196] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:27] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:12:29,592] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET / HTTP/1.1" 200 -
[2025-06-26 14:12:29,608] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,611] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,614] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/css/test-cases-styles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,616] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/css/test-suites-styles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,618] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,618] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,620] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,623] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,623] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/modules/uiUtils.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,625] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/modules/actionFormManager.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,627] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/modules/reportAndFormUtils.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,628] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/export-run.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,629] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/img/no_device.png HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,631] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/utils.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,637] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /static/js/action-manager.js?v=1750914149 HTTP/1.1" 200 -
[2025-06-26 14:12:29,639] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/modules/ElementInteractions.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,640] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/fixed-device-screen.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,642] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/execution-overlay.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,644] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/execution-manager.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,647] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/modules/TestCaseManager.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,649] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/guide/faq_guide.html HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,652] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/action-description.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,654] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/multi-step-action.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,655] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/repeat-steps-action.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,656] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/hook-action.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,659] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/modules/fallback-locators.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,662] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/modules/tap-fallback-manager.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,665] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /static/js/main.js?v=1750914149 HTTP/1.1" 200 -
[2025-06-26 14:12:29,671] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/random-data-generator.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,672] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/test_suites.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,677] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/settings.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,679] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/js/environment-variables.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,693] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-06-26 14:12:29,696] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:12:29,697] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/environments HTTP/1.1" 200 -
[2025-06-26 14:12:29,703] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-06-26 14:12:29,707] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:12:29,710] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:29,715] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/environment_variables HTTP/1.1" 200 -
[2025-06-26 14:12:29,726] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/settings HTTP/1.1" 200 -
[2025-06-26 14:12:29,730] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:29,738] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "[36mGET /static/img/favicon.ico HTTP/1.1[0m" 304 -
[2025-06-26 14:12:29,740] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/environments/current HTTP/1.1" 200 -
[2025-06-26 14:12:29,749] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:29,754] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-06-26 14:12:29,759] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:29,768] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:29,771] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/environments/5/variables HTTP/1.1" 200 -
[2025-06-26 14:12:29,777] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:29,806] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:12:29,821] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/recording/list HTTP/1.1" 200 -
[2025-06-26 14:12:29,833] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[2025-06-26 14:12:29,843] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:29] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:12:30,514] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET / HTTP/1.1" 200 -
[2025-06-26 14:12:30,529] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,532] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/css/test-cases-styles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,534] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,538] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,539] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/css/test-suites-styles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,540] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,542] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,544] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,545] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/modules/uiUtils.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,548] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/modules/actionFormManager.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,551] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/modules/reportAndFormUtils.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,553] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/img/no_device.png HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,554] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/export-run.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,555] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/utils.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,558] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /static/js/action-manager.js?v=1750914150 HTTP/1.1" 200 -
[2025-06-26 14:12:30,567] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/fixed-device-screen.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,568] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/modules/ElementInteractions.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,569] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/modules/TestCaseManager.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,570] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/execution-manager.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,570] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/execution-overlay.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,571] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/action-description.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,575] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/multi-step-action.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,575] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/guide/faq_guide.html HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,579] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/repeat-steps-action.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,581] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/hook-action.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,583] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/modules/fallback-locators.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,586] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/modules/tap-fallback-manager.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,588] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/random-data-generator.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,589] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /static/js/main.js?v=1750914150 HTTP/1.1" 200 -
[2025-06-26 14:12:30,593] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/test_suites.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,595] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/settings.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,598] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/js/environment-variables.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,613] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-06-26 14:12:30,615] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:12:30,620] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/environments HTTP/1.1" 200 -
[2025-06-26 14:12:30,627] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:30,627] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:12:30,634] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/environment_variables HTTP/1.1" 200 -
[2025-06-26 14:12:30,638] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-06-26 14:12:30,641] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/settings HTTP/1.1" 200 -
[2025-06-26 14:12:30,648] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "[36mGET /static/img/favicon.ico HTTP/1.1[0m" 304 -
[2025-06-26 14:12:30,653] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/environments/current HTTP/1.1" 200 -
[2025-06-26 14:12:30,660] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:30,666] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-06-26 14:12:30,670] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:30,678] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:30,683] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/environments/5/variables HTTP/1.1" 200 -
[2025-06-26 14:12:30,687] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:30,693] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:30,722] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:12:30,732] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/recording/list HTTP/1.1" 200 -
[2025-06-26 14:12:30,747] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[2025-06-26 14:12:30,756] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:30] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:12:31,099] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET / HTTP/1.1" 200 -
[2025-06-26 14:12:31,114] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,120] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,120] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,121] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/css/test-cases-styles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,122] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,122] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/css/test-suites-styles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,123] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,127] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,128] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/modules/uiUtils.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,130] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/modules/actionFormManager.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,133] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/modules/reportAndFormUtils.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,136] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/export-run.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,137] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/img/no_device.png HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,137] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/utils.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,142] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /static/js/action-manager.js?v=1750914151 HTTP/1.1" 200 -
[2025-06-26 14:12:31,145] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/modules/ElementInteractions.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,145] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/fixed-device-screen.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,152] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/execution-overlay.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,154] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/modules/TestCaseManager.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,155] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/execution-manager.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,157] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/guide/faq_guide.html HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,159] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/action-description.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,164] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/repeat-steps-action.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,164] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/multi-step-action.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,169] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/hook-action.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,174] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/modules/tap-fallback-manager.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,177] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/modules/fallback-locators.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,178] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /static/js/main.js?v=1750914151 HTTP/1.1" 200 -
[2025-06-26 14:12:31,184] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/random-data-generator.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,187] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/test_suites.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,189] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/settings.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,189] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/js/environment-variables.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,204] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-06-26 14:12:31,205] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:12:31,211] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /api/environments HTTP/1.1" 200 -
[2025-06-26 14:12:31,214] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:12:31,220] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:31,223] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /api/environment_variables HTTP/1.1" 200 -
[2025-06-26 14:12:31,229] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-06-26 14:12:31,238] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /api/settings HTTP/1.1" 200 -
[2025-06-26 14:12:31,243] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:31,245] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "[36mGET /static/img/favicon.ico HTTP/1.1[0m" 304 -
[2025-06-26 14:12:31,249] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /api/environments/current HTTP/1.1" 200 -
[2025-06-26 14:12:31,249] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-06-26 14:12:31,258] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:31,269] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:31,274] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /api/environments/5/variables HTTP/1.1" 200 -
[2025-06-26 14:12:31,278] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:31,287] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:31,291] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET / HTTP/1.1" 200 -
[2025-06-26 14:12:31,332] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:12:31,342] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /api/recording/list HTTP/1.1" 200 -
[2025-06-26 14:12:31,347] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:31] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[2025-06-26 14:12:32,859] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,864] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,864] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,865] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/css/test-cases-styles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,865] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/css/test-suites-styles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,868] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,872] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,874] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,877] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/modules/uiUtils.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,879] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/modules/actionFormManager.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,880] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/modules/reportAndFormUtils.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,882] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/export-run.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,887] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/img/no_device.png HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,888] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/utils.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,893] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/action-manager.js?v=1750914151 HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,893] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/modules/ElementInteractions.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,897] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/execution-overlay.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,897] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/fixed-device-screen.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,899] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/execution-manager.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,902] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/modules/TestCaseManager.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,904] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/guide/faq_guide.html HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,906] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/action-description.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,908] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/multi-step-action.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,909] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/repeat-steps-action.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,911] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/hook-action.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,916] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/modules/tap-fallback-manager.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,922] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/modules/fallback-locators.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,923] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/main.js?v=1750914151 HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,923] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/test_suites.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,924] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/random-data-generator.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,929] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/settings.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,930] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/js/environment-variables.js HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,942] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-06-26 14:12:32,948] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:12:32,949] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "GET /api/environments HTTP/1.1" 200 -
[2025-06-26 14:12:32,953] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:32,960] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:12:32,964] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "GET /api/environment_variables HTTP/1.1" 200 -
[2025-06-26 14:12:32,970] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-06-26 14:12:32,975] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "GET /api/settings HTTP/1.1" 200 -
[2025-06-26 14:12:32,980] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:32,981] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "[36mGET /static/img/favicon.ico HTTP/1.1[0m" 304 -
[2025-06-26 14:12:32,985] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "GET /api/environments/current HTTP/1.1" 200 -
[2025-06-26 14:12:32,990] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-06-26 14:12:32,994] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:32] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:33,000] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:33] "GET /api/environments/5/variables HTTP/1.1" 200 -
[2025-06-26 14:12:33,004] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:33] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:33,012] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:33] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:33,018] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:33] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:33,059] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:33] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:12:33,066] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:33] "GET /api/recording/list HTTP/1.1" 200 -
[2025-06-26 14:12:33,086] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:33] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[2025-06-26 14:12:33,094] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:33] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:12:34,688] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-06-26 14:12:34,694] INFO in appium_device_controller: Appium server is running and ready
[2025-06-26 14:12:34,694] INFO in appium_device_controller: Appium server is already running and responsive
[2025-06-26 14:12:34,695] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:34] "GET /api/devices HTTP/1.1" 200 -
[2025-06-26 14:12:36,392] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-06-26 14:12:36,396] INFO in appium_device_controller: Appium server is running and ready
[2025-06-26 14:12:36,396] INFO in appium_device_controller: Appium server is already running and responsive
[2025-06-26 14:12:36,396] INFO in appium_device_controller: Connecting to device: 00008120-00186C801E13C01E with options: None, platform hint: iOS
[2025-06-26 14:12:36,396] INFO in appium_device_controller: Connection attempt 1/3
[2025-06-26 14:12:36,396] INFO in appium_device_controller: Using provided platform hint: iOS
[2025-06-26 14:12:36,396] INFO in appium_device_controller: Using custom WebDriverAgent URL: http://localhost:8100
[2025-06-26 14:12:36,396] INFO in appium_device_controller: Desired capabilities: {'platformName': 'iOS', 'deviceName': '00008120-00186C801E13C01E', 'udid': '00008120-00186C801E13C01E', 'newCommandTimeout': 300, 'noReset': True, 'automationName': 'XCUITest', 'xcodeOrgId': '', 'xcodeSigningId': 'iPhone Developer', 'webDriverAgentUrl': 'http://localhost:8100', 'showIOSLog': True}
[2025-06-26 14:12:36,396] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'iOS', 'appium:deviceName': '00008120-00186C801E13C01E', 'appium:udid': '00008120-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8100', 'appium:showIOSLog': True}
[2025-06-26 14:12:36,396] INFO in appium_device_controller: Connecting to iOS device via WebDriverAgent
[2025-06-26 14:12:36,397] INFO in appium_device_controller: Found port 8100 for device 00008120-00186C801E13C01E in wda_ports.txt
[2025-06-26 14:12:36,397] INFO in appium_device_controller: Using WebDriverAgent URL: http://localhost:8100 for device 00008120-00186C801E13C01E
[2025-06-26 14:12:36,398] INFO in appium_device_controller: WebDriverAgent not detected at http://localhost:8100, will try to start port forwarding: HTTPConnectionPool(host='localhost', port=8100): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x11494f8a0>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-06-26 14:12:37,511] INFO in appium_device_controller: Using tidevice for port forwarding: 8100 -> 8100
[2025-06-26 14:12:37,938] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:12:37,939] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:37] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:12:37,942] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:12:37,943] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:37] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:12:39,525] INFO in appium_device_controller: tidevice port forwarding started successfully
[2025-06-26 14:12:39,537] INFO in appium_device_controller: WebDriverAgent is running at http://localhost:8100
[2025-06-26 14:12:39,537] INFO in appium_device_controller: WebDriverAgent status: {'value': {'build': {'version': '9.5.1', 'time': 'May 27 2025 13:38:32', 'productBundleIdentifier': 'com.facebook.WebDriverAgentRunner'}, 'os': {'testmanagerdVersion': 65535, 'name': 'iOS', 'sdkVersion': '18.4', 'version': '18.5'}, 'device': 'iphone', 'ios': {'ip': '************'}, 'message': 'WebDriverAgent is ready to accept commands', 'state': 'success', 'ready': True}, 'sessionId': '5E8DDD81-B7C9-490B-BF22-72F18D4FFDCC'}
[2025-06-26 14:12:39,541] INFO in appium_device_controller: Appium server is already running
[2025-06-26 14:12:39,541] INFO in appium_device_controller: iOS connection attempt 1/3
[2025-06-26 14:12:39,541] INFO in appium_device_controller: Using capabilities: {'platformName': 'iOS', 'appium:deviceName': '00008120-00186C801E13C01E', 'appium:udid': '00008120-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8100', 'appium:showIOSLog': True, 'webDriverAgentUrl': 'http://localhost:8100'}
[2025-06-26 14:12:39,546] INFO in appium_device_controller: Appium server status before connection: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '36da10ee7e9cb2a90558cad1d3933516476dc094', 'built': '2025-06-24 18:31:12 +1000'}}}
[2025-06-26 14:12:39,546] INFO in appium_device_controller: Connecting to Appium server at http://127.0.0.1:4723/wd/hub
[2025-06-26 14:12:40,099] INFO in appium_device_controller: Successfully connected to iOS device
[2025-06-26 14:12:40,099] INFO in appium_device_controller: Connected with session ID: 6450d2d8-46b8-4a55-b431-97c02001502b
[2025-06-26 14:12:40,099] INFO in appium_device_controller: Connection verified with capabilities: iOS
[2025-06-26 14:12:40,099] INFO in appium_device_controller: Initializing platform helpers for iOS
[2025-06-26 14:12:40,099] INFO in appium_device_controller: Getting device dimensions
[2025-06-26 14:12:40,474] INFO in appium_device_controller: Got device dimensions from Appium: 393x852
[2025-06-26 14:12:40,474] INFO in appium_device_controller: Device dimensions: (393, 852)
[2025-06-26 14:12:40,477] WARNING in appium_device_controller: Failed to initialize ImageMatcher: No module named 'app.utils'; 'app' is not a package
[2025-06-26 14:12:40,477] INFO in appium_device_controller: Initializing iOS-specific helpers
[2025-06-26 14:12:40,477] INFO in appium_device_controller: Setting up iOS predicate string and class chain support
[2025-06-26 14:12:40,477] INFO in appium_device_controller: iOS version: 18.0
[2025-06-26 14:12:40,477] INFO in appium_device_controller: Using modern keyboard handling for iOS 15+
[2025-06-26 14:12:40,477] INFO in appium_device_controller: Platform helpers initialization completed
[2025-06-26 14:12:40,477] INFO in appium_device_controller: Successfully connected to device on attempt 1
[2025-06-26 14:12:40,478] INFO in action_factory: Registered basic actions: tap, wait
[2025-06-26 14:12:40,479] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-06-26 14:12:40,479] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-06-26 14:12:40,479] INFO in action_factory: Registered action handler for 'multiStep'
[2025-06-26 14:12:40,479] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-06-26 14:12:40,480] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-06-26 14:12:40,480] INFO in action_factory: Registered action handler for 'swipe'
[2025-06-26 14:12:40,480] INFO in action_factory: Registered action handler for 'getParam'
[2025-06-26 14:12:40,481] INFO in action_factory: Registered action handler for 'wait'
[2025-06-26 14:12:40,481] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-06-26 14:12:40,482] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-06-26 14:12:40,482] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-06-26 14:12:40,482] INFO in action_factory: Registered action handler for 'text'
[2025-06-26 14:12:40,483] INFO in action_factory: Registered action handler for 'waitTill'
[2025-06-26 14:12:40,484] INFO in action_factory: Registered action handler for 'hookAction'
[2025-06-26 14:12:40,484] INFO in action_factory: Registered action handler for 'inputText'
[2025-06-26 14:12:40,485] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
[2025-06-26 14:12:40,486] INFO in global_values_db: Global values database initialized successfully
[2025-06-26 14:12:40,486] INFO in global_values_db: Using global values from config.py
[2025-06-26 14:12:40,486] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300}
[2025-06-26 14:12:40,488] INFO in action_factory: Registered action handler for 'setParam'
[2025-06-26 14:12:40,489] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-06-26 14:12:40,489] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-06-26 14:12:40,489] INFO in action_factory: Registered action handler for 'iosFunctions'
[2025-06-26 14:12:40,490] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-06-26 14:12:40,490] INFO in action_factory: Registered action handler for 'clickImage'
[2025-06-26 14:12:40,490] INFO in action_factory: Registered action handler for 'tap'
[2025-06-26 14:12:40,491] INFO in action_factory: Registered action handler for 'ifElseSteps'
[2025-06-26 14:12:40,491] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-06-26 14:12:40,491] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-06-26 14:12:40,492] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-06-26 14:12:40,493] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-06-26 14:12:40,493] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-06-26 14:12:40,493] INFO in action_factory: Registered action handler for 'launchApp'
[2025-06-26 14:12:40,493] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-06-26 14:12:40,493] INFO in action_factory: Registered action handler for 'info'
[2025-06-26 14:12:40,494] INFO in action_factory: Registered action handler for 'waitElement'
[2025-06-26 14:12:40,494] INFO in action_factory: Registered action handler for 'compareValue'
[2025-06-26 14:12:40,495] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-06-26 14:12:40,495] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-06-26 14:12:40,495] INFO in action_factory: Registered action handler for 'exists'
[2025-06-26 14:12:40,496] INFO in action_factory: Registered action handler for 'clickElement'
[2025-06-26 14:12:40,496] INFO in action_factory: Registered action handler for 'randomData'
[2025-06-26 14:12:40,497] INFO in action_factory: Registered action handler for 'getValue'
[2025-06-26 14:12:40,497] INFO in action_factory: Registered action handler for 'test'
[2025-06-26 14:12:40,497] INFO in action_factory: Registered action handler for 'restartApp'
[2025-06-26 14:12:40,499] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-06-26 14:12:40,499] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-06-26 14:12:40,499] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'waitTill', 'hookAction', 'inputText', 'setParam', 'repeatSteps', 'iosFunctions', 'swipeTillVisible', 'clickImage', 'ifElseSteps', 'takeScreenshot', 'tapAndType', 'tapOnText', 'launchApp', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-06-26 14:12:40,499] INFO in action_factory: Handler for 'tap': TapAction
[2025-06-26 14:12:40,499] INFO in action_factory: Handler for 'wait': WaitAction
[2025-06-26 14:12:40,499] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-06-26 14:12:40,499] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-06-26 14:12:40,499] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-06-26 14:12:40,500] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-06-26 14:12:40,500] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-06-26 14:12:40,500] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-06-26 14:12:40,500] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-06-26 14:12:40,500] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-06-26 14:12:40,500] INFO in action_factory: Handler for 'text': TextAction
[2025-06-26 14:12:40,500] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-06-26 14:12:40,501] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-06-26 14:12:40,501] INFO in action_factory: Handler for 'inputText': InputTextAction
[2025-06-26 14:12:40,501] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-06-26 14:12:40,501] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-06-26 14:12:40,501] INFO in action_factory: Handler for 'iosFunctions': IosFunctionsAction
[2025-06-26 14:12:40,501] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-06-26 14:12:40,501] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-06-26 14:12:40,501] INFO in action_factory: Handler for 'ifElseSteps': IfElseStepsAction
[2025-06-26 14:12:40,501] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-06-26 14:12:40,501] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-06-26 14:12:40,501] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-06-26 14:12:40,501] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-06-26 14:12:40,502] INFO in action_factory: Handler for 'info': InfoAction
[2025-06-26 14:12:40,502] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-06-26 14:12:40,502] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-06-26 14:12:40,502] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-06-26 14:12:40,502] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-06-26 14:12:40,502] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-06-26 14:12:40,502] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-06-26 14:12:40,502] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-06-26 14:12:40,502] INFO in action_factory: Handler for 'test': TestAction
[2025-06-26 14:12:40,502] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-06-26 14:12:40,502] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-06-26 14:12:40,502] INFO in appium_device_controller: Initializing Airtest connection for device: 00008120-00186C801E13C01E...
[2025-06-26 14:12:40,503] INFO in appium_device_controller: Connecting to iOS device with WebDriverAgent at http://localhost:8100
[2025-06-26 14:12:40,509] INFO in ios_device: Initialized MinimalIOSDevice for 00008120-00186C801E13C01E with WDA at http://localhost:8100
[2025-06-26 14:12:40,514] ERROR in appium_device_controller: Failed to get screen resolution from iOS device
[2025-06-26 14:12:40,514] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-06-26 14:12:40,514] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-06-26 14:12:40,514] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 14:12:41,728] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-06-26 14:12:41,728] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-06-26 14:12:42,102] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:42] "POST /api/device/connect HTTP/1.1" 200 -
[2025-06-26 14:12:42,937] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:12:42,938] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:42] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:12:42,941] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:12:42,942] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:42] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:12:43,119] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_00008120_00186C801E13C01E_latest.png (save_debug=False)
[2025-06-26 14:12:43,119] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 14:12:44,254] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-06-26 14:12:44,254] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_00008120_00186C801E13C01E_latest.png
[2025-06-26 14:12:44,255] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:44] "GET /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750911152934_zkxb44uh3_1750910381540_mry3dvyxe&t=1750911163116 HTTP/1.1" 200 -
[2025-06-26 14:12:47,939] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:12:47,940] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:47] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:12:47,943] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:12:47,943] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:47] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:12:52,917] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:52] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 14:12:52,937] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:12:52,938] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:52] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:12:52,940] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:12:52,941] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:52] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:12:57,348] INFO in player: Executing action: {'type': 'text', 'timestamp': 1750911172912, 'text': 'Uno card', 'enter': True}
[2025-06-26 14:12:57,348] INFO in player: DEBUG: Current test index at start of execute_action: 0
[2025-06-26 14:12:57,348] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-06-26 14:12:57,348] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-06-26 14:12:57,348] INFO in player: DEBUG: Before tracking - current_test_idx.value: 0
[2025-06-26 14:12:57,348] INFO in player: DEBUG: Using local_test_idx: 0 for tracking
[2025-06-26 14:12:57,348] INFO in player: ========== PLAYER EXECUTING ACTION WITH TEST_IDX: 0 ==========
[2025-06-26 14:12:57,348] INFO in player: ========== ACTION TYPE: text ==========
[2025-06-26 14:12:57,348] INFO in player: ========== ACTION ID:  ==========
[2025-06-26 14:12:57,348] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:12:57,348] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:12:57,348] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:12:57,348] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:12:57,348] INFO in database: DEBUG: suite_id: unknown, test_idx: 0, step_idx: 0
[2025-06-26 14:12:57,348] INFO in database: DEBUG: filename: unknown, action_type: text
[2025-06-26 14:12:57,349] INFO in database: DEBUG: status: running, retry: 0/0, in_progress: True
[2025-06-26 14:12:57,354] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py", line 1300, in execute_action
    track_test_execution(

[2025-06-26 14:12:57,354] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:12:57,354] INFO in database: DEBUG: action_params: {"type": "text", "timestamp": 1750911172912, "text": "Uno card", "enter": true}
[2025-06-26 14:12:57,355] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (unknown, 0, 0, unknown)
[2025-06-26 14:12:57,355] INFO in database: DEBUG: Existing entry found: False
[2025-06-26 14:12:57,356] INFO in database: Created execution tracking for test unknown (idx: 0, step: 0): status=running, retry=0/0
[2025-06-26 14:12:57,359] INFO in database: Updated step information in test_steps table: Step 0: text
[2025-06-26 14:12:57,361] INFO in player: Tracked execution in database: test_idx=0, step_idx=0, action_type=text, action_id=
[2025-06-26 14:12:57,361] INFO in player: Skipping device connection verification for better performance
[2025-06-26 14:12:57,361] INFO in player: Using iOS-specific text input methods for: 'Uno card'
[2025-06-26 14:12:57,361] INFO in player: Method 1: Using XCUITest mobile:typeText to input text: 'Uno card'
[2025-06-26 14:12:57,410] WARNING in player: Method 1 failed to input text using XCUITest mobile:typeText: Message: Method is not implemented
Stacktrace:
NotImplementedError: Method is not implemented
    at XCUITestDriver.execute (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/commands/execute.js:123:13)
    at runCommandPromise (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/basedriver/driver.ts:116:20)
    at /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/async-lock/lib/index.js:171:12
    at AsyncLock._promiseTry (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/async-lock/lib/index.js:306:31)
    at exec (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/async-lock/lib/index.js:170:9)
    at AsyncLock.acquire (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/async-lock/lib/index.js:189:3)
    at XCUITestDriver.executeCommand (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/basedriver/driver.ts:160:39)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at XCUITestDriver.executeCommand (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium-xcuitest-driver/lib/driver.js:1093:12)
    at defaultBehavior (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium/lib/appium.js:769:14)
    at AppiumDriver.executeWrappedCommand (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium/lib/appium.js:882:16)
    at AppiumDriver.executeCommand (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/appium/lib/appium.js:781:17)
    at asyncHandler (/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/@appium/base-driver/lib/protocol/protocol.js:389:19)
[2025-06-26 14:12:57,410] INFO in player: Method 2: Using active element to input text: 'Uno card'
[2025-06-26 14:12:57,936] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:12:57,937] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:57] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:12:57,939] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:12:57,940] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:12:57] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:12:58,952] INFO in player: Skipping delay after action execution for better performance
[2025-06-26 14:12:58,952] WARNING in player: Could not take screenshot after action: cannot access local variable 'i' where it is not associated with a value
[2025-06-26 14:12:58,952] INFO in player: DEBUG: Using local_test_idx: 0 for tracking completion
[2025-06-26 14:12:58,952] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-06-26 14:12:58,952] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-06-26 14:12:58,952] INFO in player: DEBUG: Global current_test_idx.value: 0
[2025-06-26 14:12:58,953] INFO in player: ========== PLAYER COMPLETING ACTION WITH TEST_IDX: 0 ==========
[2025-06-26 14:12:58,953] INFO in player: ========== ACTION TYPE: text ==========
[2025-06-26 14:12:58,953] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:12:58,953] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:12:58,953] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:12:58,953] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:12:58,953] INFO in database: DEBUG: suite_id: unknown, test_idx: 0, step_idx: 0
[2025-06-26 14:12:58,953] INFO in database: DEBUG: filename: unknown, action_type: text
[2025-06-26 14:12:58,953] INFO in database: DEBUG: status: passed, retry: 0/0, in_progress: False
[2025-06-26 14:12:58,954] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py", line 2954, in execute_action
    track_test_execution(

[2025-06-26 14:12:58,954] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:12:58,954] INFO in database: DEBUG: action_params: {"type": "text", "timestamp": 1750911172912, "text": "Uno card", "enter": true}
[2025-06-26 14:12:58,954] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (unknown, 0, 0, unknown)
[2025-06-26 14:12:58,955] INFO in database: DEBUG: Existing entry found: True
[2025-06-26 14:12:58,955] INFO in database: Updated execution tracking for test unknown (idx: 0, step: 0): status=passed, retry=0/0
[2025-06-26 14:12:58,958] INFO in database: Updated step information in test_steps table: Step 0: text
[2025-06-26 14:12:58,959] INFO in player: Tracked execution completion in database: test_idx=0, step_idx=0, action_type=text, status=passed
[2025-06-26 14:12:59,962] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-06-26 14:12:59,962] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-06-26 14:12:59,963] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 14:13:00,543] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-06-26 14:13:00,543] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-06-26 14:13:00,545] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:13:00,545] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:13:00,545] INFO in database: DEBUG: suite_id: , test_idx: 0, step_idx: 1
[2025-06-26 14:13:00,545] INFO in database: DEBUG: filename: unknown, action_type: text
[2025-06-26 14:13:00,545] INFO in database: DEBUG: status: in_progress, retry: 0/0, in_progress: True
[2025-06-26 14:13:00,546] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/app.py", line 3577, in execute_single_action
    track_test_execution(

[2025-06-26 14:13:00,546] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:13:00,546] INFO in database: DEBUG: action_params: {"type": "text", "timestamp": 1750911172912, "text": "Uno card", "enter": true, "action_id": "m0lInq...
[2025-06-26 14:13:00,546] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (, 0, 1, unknown)
[2025-06-26 14:13:00,546] INFO in database: DEBUG: Existing entry found: False
[2025-06-26 14:13:00,547] INFO in database: Created execution tracking for test unknown (idx: 0, step: 1): status=in_progress, retry=0/0
[2025-06-26 14:13:00,548] INFO in database: Updated step information in test_steps table: Step 1: text
[2025-06-26 14:13:00,549] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:00] "POST /api/action/execute HTTP/1.1" 200 -
[2025-06-26 14:13:00,553] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_00008120_00186C801E13C01E_latest.png (save_debug=False)
[2025-06-26 14:13:00,553] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 14:13:01,101] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/latest.png
[2025-06-26 14:13:01,101] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-06-26 14:13:01,101] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_00008120_00186C801E13C01E_latest.png
[2025-06-26 14:13:01,102] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:01] "GET /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750911152934_zkxb44uh3_1750910381540_mry3dvyxe&t=1750911180551 HTTP/1.1" 200 -
[2025-06-26 14:13:02,936] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:02,937] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:02] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:13:02,940] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:02,941] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:02] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:13:07,107] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:07] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:13:07,936] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:07,937] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:07] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:13:07,940] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:07,941] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:07] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:13:12,936] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:12,937] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:12] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:13:12,939] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:12,940] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:12] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:13:17,938] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:17,939] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:17] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:13:17,942] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:17,943] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:17] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:13:22,938] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:22,939] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:22] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:13:22,942] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:22,943] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:22] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:13:23,273] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:23] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:13:26,909] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:26] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:13:27,162] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:27] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:13:27,433] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:27] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:13:27,660] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:27] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:13:27,871] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:27] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:13:27,936] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:27,938] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:27] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:13:27,940] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:27,941] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:27] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:13:32,966] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:32,967] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:32] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:13:32,970] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:32,971] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:32] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:13:35,590] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:35] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:13:37,938] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:37,939] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:37] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:13:39,525] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:39] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:13:39,783] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:39] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:13:40,008] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:40] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:13:40,233] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:40] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 14:13:42,938] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:42,939] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:42] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:13:47,625] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-06-26 14:13:47,627] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:47] "[36mGET /static/css/test-suites-styles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:13:47,627] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:47] "[36mGET /static/css/test-cases-styles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:13:47,628] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:47] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-06-26 14:13:47,630] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:47] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:13:47,646] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:47] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-06-26 14:13:47,648] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:47] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-06-26 14:13:47,651] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:47] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
[2025-06-26 14:13:47,655] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:47] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-06-26 14:13:47,936] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:47,936] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:47] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:13:52,936] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:52,937] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:52] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:13:57,938] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:13:57,939] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:13:57] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:14:01,922] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:01] "POST /api/screenshots/delete_all HTTP/1.1" 200 -
[2025-06-26 14:14:02,937] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:14:02,938] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:02] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:14:05,275] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:05] "GET /api/recording/list HTTP/1.1" 200 -
[2025-06-26 14:14:05,298] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:05] "GET /api/recording/list HTTP/1.1" 200 -
[2025-06-26 14:14:06,767] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:06] "GET /api/test_cases/load/health2_20250408214926.json HTTP/1.1" 200 -
[2025-06-26 14:14:06,771] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:06] "POST /api/screenshots/delete_all HTTP/1.1" 200 -
[2025-06-26 14:14:07,935] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:14:07,936] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:07] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:14:08,752] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:08] "POST /api/screenshots/delete_all HTTP/1.1" 200 -
[2025-06-26 14:14:08,757] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:08] "POST /api/database/clear_screenshots HTTP/1.1" 200 -
[2025-06-26 14:14:08,763] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_00008120_00186C801E13C01E_latest.png (save_debug=False)
[2025-06-26 14:14:08,763] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 14:14:08,767] INFO in player: Executing action: {'action_id': 'ee5KkVz90e', 'package': 'com.apple.Health', 'package_id': 'com.apple.Health', 'type': 'launchApp'}
[2025-06-26 14:14:08,767] INFO in player: DEBUG: Current test index at start of execute_action: 0
[2025-06-26 14:14:08,768] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-06-26 14:14:08,768] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-06-26 14:14:08,768] INFO in player: DEBUG: Using action_id from action: ee5KkVz90e
[2025-06-26 14:14:08,768] INFO in player: DEBUG: Before tracking - current_test_idx.value: 0
[2025-06-26 14:14:08,768] INFO in player: DEBUG: Using local_test_idx: 0 for tracking
[2025-06-26 14:14:08,768] INFO in player: ========== PLAYER EXECUTING ACTION WITH TEST_IDX: 0 ==========
[2025-06-26 14:14:08,768] INFO in player: ========== ACTION TYPE: launchApp ==========
[2025-06-26 14:14:08,769] INFO in player: ========== ACTION ID: ee5KkVz90e ==========
[2025-06-26 14:14:08,769] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:08,769] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:08,769] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:14:08,769] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:14:08,769] INFO in database: DEBUG: suite_id: unknown, test_idx: 0, step_idx: 1
[2025-06-26 14:14:08,769] INFO in database: DEBUG: filename: unknown, action_type: launchApp
[2025-06-26 14:14:08,769] INFO in database: DEBUG: status: running, retry: 0/0, in_progress: True
[2025-06-26 14:14:08,769] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py", line 1300, in execute_action
    track_test_execution(

[2025-06-26 14:14:08,769] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:14:08,769] INFO in database: DEBUG: action_params: {"action_id": "ee5KkVz90e", "package": "com.apple.Health", "package_id": "com.apple.Health", "type":...
[2025-06-26 14:14:08,770] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (unknown, 0, 1, unknown)
[2025-06-26 14:14:08,770] INFO in database: DEBUG: Existing entry found: False
[2025-06-26 14:14:08,770] INFO in database: Created execution tracking for test unknown (idx: 0, step: 1): status=running, retry=0/0
[2025-06-26 14:14:08,773] INFO in database: Updated step information in test_steps table: Step 1: launchApp
[2025-06-26 14:14:08,774] INFO in player: Tracked execution in database: test_idx=0, step_idx=1, action_type=launchApp, action_id=ee5KkVz90e
[2025-06-26 14:14:08,774] INFO in player: Skipping device connection verification for better performance
[2025-06-26 14:14:08,775] INFO in player: Launching app: com.apple.Health
[2025-06-26 14:14:08,777] WARNING in player: Airtest start_app failed: 'No devices added.', falling back to ADB
[2025-06-26 14:14:08,777] INFO in appium_device_controller: Launching app: com.apple.Health
[2025-06-26 14:14:08,777] INFO in appium_device_controller: Using XCUITest to launch iOS app: com.apple.Health
[2025-06-26 14:14:10,146] INFO in player: Skipping delay after action execution for better performance
[2025-06-26 14:14:10,146] WARNING in player: Could not take screenshot after action: cannot access local variable 'i' where it is not associated with a value
[2025-06-26 14:14:10,146] INFO in player: DEBUG: Using local_test_idx: 0 for tracking completion
[2025-06-26 14:14:10,146] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-06-26 14:14:10,146] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-06-26 14:14:10,146] INFO in player: DEBUG: Global current_test_idx.value: 0
[2025-06-26 14:14:10,147] INFO in player: ========== PLAYER COMPLETING ACTION WITH TEST_IDX: 0 ==========
[2025-06-26 14:14:10,147] INFO in player: ========== ACTION TYPE: launchApp ==========
[2025-06-26 14:14:10,147] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:10,147] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:10,147] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:14:10,147] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:14:10,147] INFO in database: DEBUG: suite_id: unknown, test_idx: 0, step_idx: 1
[2025-06-26 14:14:10,147] INFO in database: DEBUG: filename: unknown, action_type: launchApp
[2025-06-26 14:14:10,147] INFO in database: DEBUG: status: passed, retry: 0/0, in_progress: False
[2025-06-26 14:14:10,154] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py", line 2954, in execute_action
    track_test_execution(

[2025-06-26 14:14:10,155] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:14:10,155] INFO in database: DEBUG: action_params: {"action_id": "ee5KkVz90e", "package": "com.apple.Health", "package_id": "com.apple.Health", "type":...
[2025-06-26 14:14:10,156] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (unknown, 0, 1, unknown)
[2025-06-26 14:14:10,156] INFO in database: DEBUG: Existing entry found: True
[2025-06-26 14:14:10,156] INFO in database: Updated execution tracking for test unknown (idx: 0, step: 1): status=passed, retry=0/0
[2025-06-26 14:14:10,156] INFO in database: Extracted action_id from action_params: ee5KkVz90e
[2025-06-26 14:14:10,159] INFO in database: Updated step information in test_steps table: Step 1: launchApp
[2025-06-26 14:14:10,160] INFO in player: Tracked execution completion in database: test_idx=0, step_idx=1, action_type=launchApp, status=passed
[2025-06-26 14:14:10,521] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/latest.png
[2025-06-26 14:14:10,521] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-06-26 14:14:10,522] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_00008120_00186C801E13C01E_latest.png
[2025-06-26 14:14:10,522] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:10] "GET /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750911152934_zkxb44uh3_1750910381540_mry3dvyxe&t=1750911180551 HTTP/1.1" 200 -
[2025-06-26 14:14:11,161] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots
[2025-06-26 14:14:11,161] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-06-26 14:14:11,161] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/placeholder.png (save_debug=False)
[2025-06-26 14:14:11,161] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 14:14:11,709] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/latest.png
[2025-06-26 14:14:11,710] INFO in appium_device_controller: === SAVING SCREENSHOT TO DATABASE ===
[2025-06-26 14:14:11,710] INFO in appium_device_controller: suite_id: 
[2025-06-26 14:14:11,710] INFO in appium_device_controller: test_idx: 0
[2025-06-26 14:14:11,710] INFO in appium_device_controller: step_idx: 1
[2025-06-26 14:14:11,710] INFO in appium_device_controller: filename: placeholder.png
[2025-06-26 14:14:11,710] INFO in appium_device_controller: action_id: placeholder
[2025-06-26 14:14:11,710] INFO in database: Saving screenshot with action_id: placeholder
[2025-06-26 14:14:11,711] INFO in database: Inserting new screenshot record with action_id: placeholder
[2025-06-26 14:14:11,712] INFO in database: Saved screenshot info to database: placeholder.png with action_id: placeholder
[2025-06-26 14:14:11,712] INFO in appium_device_controller: Saved screenshot info to database for step 0_1
[2025-06-26 14:14:11,712] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-06-26 14:14:11,713] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:14:11,713] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:14:11,714] INFO in database: DEBUG: suite_id: , test_idx: 0, step_idx: 2
[2025-06-26 14:14:11,714] INFO in database: DEBUG: filename: unknown, action_type: launchApp
[2025-06-26 14:14:11,714] INFO in database: DEBUG: status: in_progress, retry: 0/0, in_progress: True
[2025-06-26 14:14:11,714] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/app.py", line 3577, in execute_single_action
    track_test_execution(

[2025-06-26 14:14:11,714] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:14:11,714] INFO in database: DEBUG: action_params: {"action_id": "ee5KkVz90e", "package": "com.apple.Health", "package_id": "com.apple.Health", "type":...
[2025-06-26 14:14:11,714] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (, 0, 2, unknown)
[2025-06-26 14:14:11,715] INFO in database: DEBUG: Existing entry found: False
[2025-06-26 14:14:11,715] INFO in database: Created execution tracking for test unknown (idx: 0, step: 2): status=in_progress, retry=0/0
[2025-06-26 14:14:11,717] INFO in database: Updated step information in test_steps table: Step 2: launchApp
[2025-06-26 14:14:11,718] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:11] "POST /api/action/execute HTTP/1.1" 200 -
[2025-06-26 14:14:11,721] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_00008120_00186C801E13C01E_latest.png (save_debug=False)
[2025-06-26 14:14:11,721] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 14:14:12,223] INFO in player: Executing action: {'action_id': 'E5An5BbVuK', 'locator_type': 'xpath', 'locator_value': '//XCUIElementTypeStaticText[@name="Edit"]', 'timeout': 10, 'type': 'clickElement'}
[2025-06-26 14:14:12,223] INFO in player: DEBUG: Current test index at start of execute_action: 0
[2025-06-26 14:14:12,224] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-06-26 14:14:12,224] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-06-26 14:14:12,224] INFO in player: DEBUG: Using action_id from action: E5An5BbVuK
[2025-06-26 14:14:12,224] INFO in player: DEBUG: Before tracking - current_test_idx.value: 0
[2025-06-26 14:14:12,224] INFO in player: DEBUG: Using local_test_idx: 0 for tracking
[2025-06-26 14:14:12,224] INFO in player: ========== PLAYER EXECUTING ACTION WITH TEST_IDX: 0 ==========
[2025-06-26 14:14:12,224] INFO in player: ========== ACTION TYPE: clickElement ==========
[2025-06-26 14:14:12,224] INFO in player: ========== ACTION ID: E5An5BbVuK ==========
[2025-06-26 14:14:12,224] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:12,224] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:12,224] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:14:12,224] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:14:12,224] INFO in database: DEBUG: suite_id: unknown, test_idx: 0, step_idx: 2
[2025-06-26 14:14:12,224] INFO in database: DEBUG: filename: unknown, action_type: clickElement
[2025-06-26 14:14:12,224] INFO in database: DEBUG: status: running, retry: 0/0, in_progress: True
[2025-06-26 14:14:12,224] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py", line 1300, in execute_action
    track_test_execution(

[2025-06-26 14:14:12,225] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:14:12,225] INFO in database: DEBUG: action_params: {"action_id": "E5An5BbVuK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@...
[2025-06-26 14:14:12,225] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (unknown, 0, 2, unknown)
[2025-06-26 14:14:12,225] INFO in database: DEBUG: Existing entry found: False
[2025-06-26 14:14:12,225] INFO in database: Created execution tracking for test unknown (idx: 0, step: 2): status=running, retry=0/0
[2025-06-26 14:14:12,227] INFO in database: Updated step information in test_steps table: Step 2: clickElement
[2025-06-26 14:14:12,228] INFO in player: Tracked execution in database: test_idx=0, step_idx=2, action_type=clickElement, action_id=E5An5BbVuK
[2025-06-26 14:14:12,228] INFO in player: Skipping device connection verification for better performance
[2025-06-26 14:14:12,228] INFO in player: Clicking element with xpath: //XCUIElementTypeStaticText[@name="Edit"], timeout=10s
[2025-06-26 14:14:12,228] INFO in player: Clicking element with XPath: //XCUIElementTypeStaticText[@name="Edit"]
[2025-06-26 14:14:12,228] INFO in player: Waiting for element to be clickable: ('xpath', '//XCUIElementTypeStaticText[@name="Edit"]') (Timeout: 10s)
[2025-06-26 14:14:12,303] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/latest.png
[2025-06-26 14:14:12,303] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-06-26 14:14:12,303] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_00008120_00186C801E13C01E_latest.png
[2025-06-26 14:14:12,304] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:12] "GET /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750911152934_zkxb44uh3_1750910381540_mry3dvyxe&t=1750911251719 HTTP/1.1" 200 -
[2025-06-26 14:14:12,666] INFO in player: Element is clickable, attempting click...
[2025-06-26 14:14:13,393] INFO in player: Element clicked: Element XCUIElementTypeStaticText with text 'Edit'
[2025-06-26 14:14:13,393] INFO in player: Skipping delay after action execution for better performance
[2025-06-26 14:14:13,394] WARNING in player: Could not take screenshot after action: cannot access local variable 'i' where it is not associated with a value
[2025-06-26 14:14:13,394] INFO in player: DEBUG: Using local_test_idx: 0 for tracking completion
[2025-06-26 14:14:13,394] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-06-26 14:14:13,394] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-06-26 14:14:13,394] INFO in player: DEBUG: Global current_test_idx.value: 0
[2025-06-26 14:14:13,394] INFO in player: ========== PLAYER COMPLETING ACTION WITH TEST_IDX: 0 ==========
[2025-06-26 14:14:13,394] INFO in player: ========== ACTION TYPE: clickElement ==========
[2025-06-26 14:14:13,394] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:13,394] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:13,394] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:14:13,394] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:14:13,395] INFO in database: DEBUG: suite_id: unknown, test_idx: 0, step_idx: 2
[2025-06-26 14:14:13,395] INFO in database: DEBUG: filename: unknown, action_type: clickElement
[2025-06-26 14:14:13,395] INFO in database: DEBUG: status: passed, retry: 0/0, in_progress: False
[2025-06-26 14:14:13,395] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py", line 2954, in execute_action
    track_test_execution(

[2025-06-26 14:14:13,395] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:14:13,395] INFO in database: DEBUG: action_params: {"action_id": "E5An5BbVuK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@...
[2025-06-26 14:14:13,395] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (unknown, 0, 2, unknown)
[2025-06-26 14:14:13,396] INFO in database: DEBUG: Existing entry found: True
[2025-06-26 14:14:13,396] INFO in database: Updated execution tracking for test unknown (idx: 0, step: 2): status=passed, retry=0/0
[2025-06-26 14:14:13,396] INFO in database: Extracted action_id from action_params: E5An5BbVuK
[2025-06-26 14:14:13,398] INFO in database: Updated step information in test_steps table: Step 2: clickElement
[2025-06-26 14:14:13,399] INFO in player: Tracked execution completion in database: test_idx=0, step_idx=2, action_type=clickElement, status=passed
[2025-06-26 14:14:14,400] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots
[2025-06-26 14:14:14,400] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-06-26 14:14:14,400] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/placeholder.png (save_debug=False)
[2025-06-26 14:14:14,400] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 14:14:14,845] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/latest.png
[2025-06-26 14:14:14,846] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-06-26 14:14:14,846] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-06-26 14:14:14,847] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:14:14,847] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:14:14,847] INFO in database: DEBUG: suite_id: , test_idx: 0, step_idx: 3
[2025-06-26 14:14:14,847] INFO in database: DEBUG: filename: unknown, action_type: clickElement
[2025-06-26 14:14:14,847] INFO in database: DEBUG: status: in_progress, retry: 0/0, in_progress: True
[2025-06-26 14:14:14,847] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/app.py", line 3577, in execute_single_action
    track_test_execution(

[2025-06-26 14:14:14,847] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:14:14,847] INFO in database: DEBUG: action_params: {"action_id": "E5An5BbVuK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@...
[2025-06-26 14:14:14,848] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (, 0, 3, unknown)
[2025-06-26 14:14:14,848] INFO in database: DEBUG: Existing entry found: False
[2025-06-26 14:14:14,848] INFO in database: Created execution tracking for test unknown (idx: 0, step: 3): status=in_progress, retry=0/0
[2025-06-26 14:14:14,850] INFO in database: Updated step information in test_steps table: Step 3: clickElement
[2025-06-26 14:14:14,851] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:14] "POST /api/action/execute HTTP/1.1" 200 -
[2025-06-26 14:14:14,854] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_00008120_00186C801E13C01E_latest.png (save_debug=False)
[2025-06-26 14:14:14,854] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 14:14:15,281] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/latest.png
[2025-06-26 14:14:15,281] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-06-26 14:14:15,281] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_00008120_00186C801E13C01E_latest.png
[2025-06-26 14:14:15,281] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:15] "GET /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750911152934_zkxb44uh3_1750910381540_mry3dvyxe&t=1750911254852 HTTP/1.1" 200 -
[2025-06-26 14:14:15,359] INFO in player: Executing action: {'action_id': 'KfOSdvcOkk', 'locator_type': 'xpath', 'locator_value': ' //XCUIElementTypeButton[@name="Done"]', 'timeout': 10, 'type': 'clickElement'}
[2025-06-26 14:14:15,359] INFO in player: DEBUG: Current test index at start of execute_action: 0
[2025-06-26 14:14:15,359] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-06-26 14:14:15,360] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-06-26 14:14:15,360] INFO in player: DEBUG: Using action_id from action: KfOSdvcOkk
[2025-06-26 14:14:15,360] INFO in player: DEBUG: Before tracking - current_test_idx.value: 0
[2025-06-26 14:14:15,360] INFO in player: DEBUG: Using local_test_idx: 0 for tracking
[2025-06-26 14:14:15,360] INFO in player: ========== PLAYER EXECUTING ACTION WITH TEST_IDX: 0 ==========
[2025-06-26 14:14:15,360] INFO in player: ========== ACTION TYPE: clickElement ==========
[2025-06-26 14:14:15,360] INFO in player: ========== ACTION ID: KfOSdvcOkk ==========
[2025-06-26 14:14:15,360] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:15,360] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:15,360] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:14:15,360] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:14:15,360] INFO in database: DEBUG: suite_id: unknown, test_idx: 0, step_idx: 3
[2025-06-26 14:14:15,360] INFO in database: DEBUG: filename: unknown, action_type: clickElement
[2025-06-26 14:14:15,360] INFO in database: DEBUG: status: running, retry: 0/0, in_progress: True
[2025-06-26 14:14:15,361] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py", line 1300, in execute_action
    track_test_execution(

[2025-06-26 14:14:15,361] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:14:15,361] INFO in database: DEBUG: action_params: {"action_id": "KfOSdvcOkk", "locator_type": "xpath", "locator_value": " //XCUIElementTypeButton[@nam...
[2025-06-26 14:14:15,361] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (unknown, 0, 3, unknown)
[2025-06-26 14:14:15,362] INFO in database: DEBUG: Existing entry found: False
[2025-06-26 14:14:15,362] INFO in database: Created execution tracking for test unknown (idx: 0, step: 3): status=running, retry=0/0
[2025-06-26 14:14:15,364] INFO in database: Updated step information in test_steps table: Step 3: clickElement
[2025-06-26 14:14:15,365] INFO in player: Tracked execution in database: test_idx=0, step_idx=3, action_type=clickElement, action_id=KfOSdvcOkk
[2025-06-26 14:14:15,365] INFO in player: Skipping device connection verification for better performance
[2025-06-26 14:14:15,365] INFO in player: Clicking element with xpath:  //XCUIElementTypeButton[@name="Done"], timeout=10s
[2025-06-26 14:14:15,365] INFO in player: Clicking element with XPath:  //XCUIElementTypeButton[@name="Done"]
[2025-06-26 14:14:15,365] INFO in player: Waiting for element to be clickable: ('xpath', ' //XCUIElementTypeButton[@name="Done"]') (Timeout: 10s)
[2025-06-26 14:14:16,040] INFO in player: Element is clickable, attempting click...
[2025-06-26 14:14:16,965] INFO in player: Element clicked: Element XCUIElementTypeButton
[2025-06-26 14:14:16,965] INFO in player: Skipping delay after action execution for better performance
[2025-06-26 14:14:16,965] WARNING in player: Could not take screenshot after action: cannot access local variable 'i' where it is not associated with a value
[2025-06-26 14:14:16,965] INFO in player: DEBUG: Using local_test_idx: 0 for tracking completion
[2025-06-26 14:14:16,965] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-06-26 14:14:16,965] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-06-26 14:14:16,965] INFO in player: DEBUG: Global current_test_idx.value: 0
[2025-06-26 14:14:16,965] INFO in player: ========== PLAYER COMPLETING ACTION WITH TEST_IDX: 0 ==========
[2025-06-26 14:14:16,965] INFO in player: ========== ACTION TYPE: clickElement ==========
[2025-06-26 14:14:16,965] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:16,966] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:16,966] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:14:16,966] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:14:16,966] INFO in database: DEBUG: suite_id: unknown, test_idx: 0, step_idx: 3
[2025-06-26 14:14:16,966] INFO in database: DEBUG: filename: unknown, action_type: clickElement
[2025-06-26 14:14:16,966] INFO in database: DEBUG: status: passed, retry: 0/0, in_progress: False
[2025-06-26 14:14:16,966] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py", line 2954, in execute_action
    track_test_execution(

[2025-06-26 14:14:16,966] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:14:16,966] INFO in database: DEBUG: action_params: {"action_id": "KfOSdvcOkk", "locator_type": "xpath", "locator_value": " //XCUIElementTypeButton[@nam...
[2025-06-26 14:14:16,967] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (unknown, 0, 3, unknown)
[2025-06-26 14:14:16,967] INFO in database: DEBUG: Existing entry found: True
[2025-06-26 14:14:16,967] INFO in database: Updated execution tracking for test unknown (idx: 0, step: 3): status=passed, retry=0/0
[2025-06-26 14:14:16,967] INFO in database: Extracted action_id from action_params: KfOSdvcOkk
[2025-06-26 14:14:16,969] INFO in database: Updated step information in test_steps table: Step 3: clickElement
[2025-06-26 14:14:16,970] INFO in player: Tracked execution completion in database: test_idx=0, step_idx=3, action_type=clickElement, status=passed
[2025-06-26 14:14:17,975] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots
[2025-06-26 14:14:17,975] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-06-26 14:14:17,975] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/placeholder.png (save_debug=False)
[2025-06-26 14:14:17,975] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 14:14:18,533] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/latest.png
[2025-06-26 14:14:18,534] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-06-26 14:14:18,534] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-06-26 14:14:18,535] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:14:18,535] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:14:18,535] INFO in database: DEBUG: suite_id: , test_idx: 0, step_idx: 4
[2025-06-26 14:14:18,535] INFO in database: DEBUG: filename: unknown, action_type: clickElement
[2025-06-26 14:14:18,535] INFO in database: DEBUG: status: in_progress, retry: 0/0, in_progress: True
[2025-06-26 14:14:18,536] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/app.py", line 3577, in execute_single_action
    track_test_execution(

[2025-06-26 14:14:18,536] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:14:18,536] INFO in database: DEBUG: action_params: {"action_id": "KfOSdvcOkk", "locator_type": "xpath", "locator_value": " //XCUIElementTypeButton[@nam...
[2025-06-26 14:14:18,536] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (, 0, 4, unknown)
[2025-06-26 14:14:18,536] INFO in database: DEBUG: Existing entry found: False
[2025-06-26 14:14:18,536] INFO in database: Created execution tracking for test unknown (idx: 0, step: 4): status=in_progress, retry=0/0
[2025-06-26 14:14:18,538] INFO in database: Updated step information in test_steps table: Step 4: clickElement
[2025-06-26 14:14:18,539] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:18] "POST /api/action/execute HTTP/1.1" 200 -
[2025-06-26 14:14:18,542] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_00008120_00186C801E13C01E_latest.png (save_debug=False)
[2025-06-26 14:14:18,542] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 14:14:19,045] INFO in player: Executing action: {'action_id': '4kBvNvFi5i', 'duration': 1, 'time': 1, 'timestamp': 1747996417607, 'type': 'wait'}
[2025-06-26 14:14:19,045] INFO in player: DEBUG: Current test index at start of execute_action: 0
[2025-06-26 14:14:19,045] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-06-26 14:14:19,045] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-06-26 14:14:19,045] INFO in player: DEBUG: Using action_id from action: 4kBvNvFi5i
[2025-06-26 14:14:19,045] INFO in player: DEBUG: Before tracking - current_test_idx.value: 0
[2025-06-26 14:14:19,045] INFO in player: DEBUG: Using local_test_idx: 0 for tracking
[2025-06-26 14:14:19,045] INFO in player: ========== PLAYER EXECUTING ACTION WITH TEST_IDX: 0 ==========
[2025-06-26 14:14:19,045] INFO in player: ========== ACTION TYPE: wait ==========
[2025-06-26 14:14:19,045] INFO in player: ========== ACTION ID: 4kBvNvFi5i ==========
[2025-06-26 14:14:19,045] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:19,045] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:19,046] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:14:19,046] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:14:19,046] INFO in database: DEBUG: suite_id: unknown, test_idx: 0, step_idx: 4
[2025-06-26 14:14:19,046] INFO in database: DEBUG: filename: unknown, action_type: wait
[2025-06-26 14:14:19,046] INFO in database: DEBUG: status: running, retry: 0/0, in_progress: True
[2025-06-26 14:14:19,046] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py", line 1300, in execute_action
    track_test_execution(

[2025-06-26 14:14:19,046] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:14:19,046] INFO in database: DEBUG: action_params: {"action_id": "4kBvNvFi5i", "duration": 1, "time": 1, "timestamp": 1747996417607, "type": "wait"}
[2025-06-26 14:14:19,047] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (unknown, 0, 4, unknown)
[2025-06-26 14:14:19,047] INFO in database: DEBUG: Existing entry found: False
[2025-06-26 14:14:19,047] INFO in database: Created execution tracking for test unknown (idx: 0, step: 4): status=running, retry=0/0
[2025-06-26 14:14:19,049] INFO in database: Updated step information in test_steps table: Step 4: wait
[2025-06-26 14:14:19,050] INFO in player: Tracked execution in database: test_idx=0, step_idx=4, action_type=wait, action_id=4kBvNvFi5i
[2025-06-26 14:14:19,050] INFO in player: Skipping device connection verification for better performance
[2025-06-26 14:14:19,095] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/latest.png
[2025-06-26 14:14:19,095] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-06-26 14:14:19,095] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_00008120_00186C801E13C01E_latest.png
[2025-06-26 14:14:19,096] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:19] "GET /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750911152934_zkxb44uh3_1750910381540_mry3dvyxe&t=1750911258540 HTTP/1.1" 200 -
[2025-06-26 14:14:20,050] INFO in player: Skipping delay after action execution for better performance
[2025-06-26 14:14:20,050] WARNING in player: Could not take screenshot after action: cannot access local variable 'i' where it is not associated with a value
[2025-06-26 14:14:20,051] INFO in player: DEBUG: Using local_test_idx: 0 for tracking completion
[2025-06-26 14:14:20,051] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-06-26 14:14:20,051] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-06-26 14:14:20,051] INFO in player: DEBUG: Global current_test_idx.value: 0
[2025-06-26 14:14:20,051] INFO in player: ========== PLAYER COMPLETING ACTION WITH TEST_IDX: 0 ==========
[2025-06-26 14:14:20,051] INFO in player: ========== ACTION TYPE: wait ==========
[2025-06-26 14:14:20,051] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:20,051] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:20,052] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:14:20,052] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:14:20,052] INFO in database: DEBUG: suite_id: unknown, test_idx: 0, step_idx: 4
[2025-06-26 14:14:20,052] INFO in database: DEBUG: filename: unknown, action_type: wait
[2025-06-26 14:14:20,052] INFO in database: DEBUG: status: passed, retry: 0/0, in_progress: False
[2025-06-26 14:14:20,053] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py", line 2954, in execute_action
    track_test_execution(

[2025-06-26 14:14:20,053] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:14:20,053] INFO in database: DEBUG: action_params: {"action_id": "4kBvNvFi5i", "duration": 1, "time": 1, "timestamp": 1747996417607, "type": "wait"}
[2025-06-26 14:14:20,053] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (unknown, 0, 4, unknown)
[2025-06-26 14:14:20,054] INFO in database: DEBUG: Existing entry found: True
[2025-06-26 14:14:20,054] INFO in database: Updated execution tracking for test unknown (idx: 0, step: 4): status=passed, retry=0/0
[2025-06-26 14:14:20,054] INFO in database: Extracted action_id from action_params: 4kBvNvFi5i
[2025-06-26 14:14:20,056] INFO in database: Updated step information in test_steps table: Step 4: wait
[2025-06-26 14:14:20,058] INFO in player: Tracked execution completion in database: test_idx=0, step_idx=4, action_type=wait, status=passed
[2025-06-26 14:14:21,061] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots
[2025-06-26 14:14:21,061] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-06-26 14:14:21,061] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/placeholder.png (save_debug=False)
[2025-06-26 14:14:21,061] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 14:14:21,615] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/latest.png
[2025-06-26 14:14:21,616] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-06-26 14:14:21,616] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-06-26 14:14:21,617] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:14:21,617] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:14:21,617] INFO in database: DEBUG: suite_id: , test_idx: 0, step_idx: 5
[2025-06-26 14:14:21,617] INFO in database: DEBUG: filename: unknown, action_type: wait
[2025-06-26 14:14:21,617] INFO in database: DEBUG: status: in_progress, retry: 0/0, in_progress: True
[2025-06-26 14:14:21,618] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/app.py", line 3577, in execute_single_action
    track_test_execution(

[2025-06-26 14:14:21,618] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:14:21,618] INFO in database: DEBUG: action_params: {"action_id": "4kBvNvFi5i", "duration": 1, "time": 1, "timestamp": 1747996417607, "type": "wait"}
[2025-06-26 14:14:21,618] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (, 0, 5, unknown)
[2025-06-26 14:14:21,618] INFO in database: DEBUG: Existing entry found: False
[2025-06-26 14:14:21,619] INFO in database: Created execution tracking for test unknown (idx: 0, step: 5): status=in_progress, retry=0/0
[2025-06-26 14:14:21,620] INFO in database: Updated step information in test_steps table: Step 5: wait
[2025-06-26 14:14:21,621] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:21] "POST /api/action/execute HTTP/1.1" 200 -
[2025-06-26 14:14:21,625] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_00008120_00186C801E13C01E_latest.png (save_debug=False)
[2025-06-26 14:14:21,625] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 14:14:22,128] INFO in player: Executing action: {'action_id': 'yvWe991wY2', 'package': 'com.apple.Health', 'package_id': 'com.apple.Health', 'type': 'terminateApp'}
[2025-06-26 14:14:22,128] INFO in player: DEBUG: Current test index at start of execute_action: 0
[2025-06-26 14:14:22,128] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-06-26 14:14:22,128] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-06-26 14:14:22,128] INFO in player: DEBUG: Using action_id from action: yvWe991wY2
[2025-06-26 14:14:22,129] INFO in player: DEBUG: Before tracking - current_test_idx.value: 0
[2025-06-26 14:14:22,129] INFO in player: DEBUG: Using local_test_idx: 0 for tracking
[2025-06-26 14:14:22,129] INFO in player: ========== PLAYER EXECUTING ACTION WITH TEST_IDX: 0 ==========
[2025-06-26 14:14:22,129] INFO in player: ========== ACTION TYPE: terminateApp ==========
[2025-06-26 14:14:22,129] INFO in player: ========== ACTION ID: yvWe991wY2 ==========
[2025-06-26 14:14:22,129] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:22,129] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:22,129] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:14:22,129] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:14:22,129] INFO in database: DEBUG: suite_id: unknown, test_idx: 0, step_idx: 5
[2025-06-26 14:14:22,129] INFO in database: DEBUG: filename: unknown, action_type: terminateApp
[2025-06-26 14:14:22,129] INFO in database: DEBUG: status: running, retry: 0/0, in_progress: True
[2025-06-26 14:14:22,130] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py", line 1300, in execute_action
    track_test_execution(

[2025-06-26 14:14:22,130] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:14:22,130] INFO in database: DEBUG: action_params: {"action_id": "yvWe991wY2", "package": "com.apple.Health", "package_id": "com.apple.Health", "type":...
[2025-06-26 14:14:22,130] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (unknown, 0, 5, unknown)
[2025-06-26 14:14:22,131] INFO in database: DEBUG: Existing entry found: False
[2025-06-26 14:14:22,131] INFO in database: Created execution tracking for test unknown (idx: 0, step: 5): status=running, retry=0/0
[2025-06-26 14:14:22,132] INFO in database: Updated step information in test_steps table: Step 5: terminateApp
[2025-06-26 14:14:22,133] INFO in player: Tracked execution in database: test_idx=0, step_idx=5, action_type=terminateApp, action_id=yvWe991wY2
[2025-06-26 14:14:22,133] INFO in player: Skipping device connection verification for better performance
[2025-06-26 14:14:22,133] INFO in player: Terminating app: com.apple.Health
[2025-06-26 14:14:22,134] WARNING in player: Airtest stop_app failed: 'No devices added.', falling back to ADB
[2025-06-26 14:14:22,135] INFO in appium_device_controller: Terminating app: com.apple.Health
[2025-06-26 14:14:22,135] INFO in appium_device_controller: Using XCUITest to terminate iOS app: com.apple.Health
[2025-06-26 14:14:22,179] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/latest.png
[2025-06-26 14:14:22,179] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-06-26 14:14:22,179] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_00008120_00186C801E13C01E_latest.png
[2025-06-26 14:14:22,180] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:22] "GET /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750911152934_zkxb44uh3_1750910381540_mry3dvyxe&t=1750911261623 HTTP/1.1" 200 -
[2025-06-26 14:14:23,236] INFO in player: Skipping delay after action execution for better performance
[2025-06-26 14:14:23,237] WARNING in player: Could not take screenshot after action: cannot access local variable 'i' where it is not associated with a value
[2025-06-26 14:14:23,237] INFO in player: DEBUG: Using local_test_idx: 0 for tracking completion
[2025-06-26 14:14:23,237] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-06-26 14:14:23,237] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-06-26 14:14:23,237] INFO in player: DEBUG: Global current_test_idx.value: 0
[2025-06-26 14:14:23,237] INFO in player: ========== PLAYER COMPLETING ACTION WITH TEST_IDX: 0 ==========
[2025-06-26 14:14:23,237] INFO in player: ========== ACTION TYPE: terminateApp ==========
[2025-06-26 14:14:23,237] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:23,237] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:23,237] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:14:23,237] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:14:23,237] INFO in database: DEBUG: suite_id: unknown, test_idx: 0, step_idx: 5
[2025-06-26 14:14:23,237] INFO in database: DEBUG: filename: unknown, action_type: terminateApp
[2025-06-26 14:14:23,237] INFO in database: DEBUG: status: passed, retry: 0/0, in_progress: False
[2025-06-26 14:14:23,238] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py", line 2954, in execute_action
    track_test_execution(

[2025-06-26 14:14:23,238] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:14:23,238] INFO in database: DEBUG: action_params: {"action_id": "yvWe991wY2", "package": "com.apple.Health", "package_id": "com.apple.Health", "type":...
[2025-06-26 14:14:23,238] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (unknown, 0, 5, unknown)
[2025-06-26 14:14:23,239] INFO in database: DEBUG: Existing entry found: True
[2025-06-26 14:14:23,239] INFO in database: Updated execution tracking for test unknown (idx: 0, step: 5): status=passed, retry=0/0
[2025-06-26 14:14:23,239] INFO in database: Extracted action_id from action_params: yvWe991wY2
[2025-06-26 14:14:23,241] INFO in database: Updated step information in test_steps table: Step 5: terminateApp
[2025-06-26 14:14:23,242] INFO in player: Tracked execution completion in database: test_idx=0, step_idx=5, action_type=terminateApp, status=passed
[2025-06-26 14:14:24,247] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots
[2025-06-26 14:14:24,247] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-06-26 14:14:24,247] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/placeholder.png (save_debug=False)
[2025-06-26 14:14:24,247] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 14:14:25,399] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/latest.png
[2025-06-26 14:14:25,399] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-06-26 14:14:25,399] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-06-26 14:14:25,402] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:14:25,402] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:14:25,402] INFO in database: DEBUG: suite_id: , test_idx: 0, step_idx: 6
[2025-06-26 14:14:25,402] INFO in database: DEBUG: filename: unknown, action_type: terminateApp
[2025-06-26 14:14:25,402] INFO in database: DEBUG: status: in_progress, retry: 0/0, in_progress: True
[2025-06-26 14:14:25,402] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/app.py", line 3577, in execute_single_action
    track_test_execution(

[2025-06-26 14:14:25,403] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:14:25,403] INFO in database: DEBUG: action_params: {"action_id": "yvWe991wY2", "package": "com.apple.Health", "package_id": "com.apple.Health", "type":...
[2025-06-26 14:14:25,403] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (, 0, 6, unknown)
[2025-06-26 14:14:25,403] INFO in database: DEBUG: Existing entry found: False
[2025-06-26 14:14:25,404] INFO in database: Created execution tracking for test unknown (idx: 0, step: 6): status=in_progress, retry=0/0
[2025-06-26 14:14:25,405] INFO in database: Updated step information in test_steps table: Step 6: terminateApp
[2025-06-26 14:14:25,407] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:25] "POST /api/action/execute HTTP/1.1" 200 -
[2025-06-26 14:14:25,410] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_00008120_00186C801E13C01E_latest.png (save_debug=False)
[2025-06-26 14:14:25,410] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 14:14:25,912] INFO in player: Executing action: {'action_id': 't4fslOCE8y', 'text': 'This is just a test global[Test Case Delay]', 'timestamp': 1750909536474, 'type': 'info'}
[2025-06-26 14:14:25,913] INFO in player: DEBUG: Current test index at start of execute_action: 0
[2025-06-26 14:14:25,913] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-06-26 14:14:25,913] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-06-26 14:14:25,913] INFO in player: DEBUG: Using action_id from action: t4fslOCE8y
[2025-06-26 14:14:25,913] INFO in player: DEBUG: Before tracking - current_test_idx.value: 0
[2025-06-26 14:14:25,913] INFO in player: DEBUG: Using local_test_idx: 0 for tracking
[2025-06-26 14:14:25,913] INFO in player: ========== PLAYER EXECUTING ACTION WITH TEST_IDX: 0 ==========
[2025-06-26 14:14:25,913] INFO in player: ========== ACTION TYPE: info ==========
[2025-06-26 14:14:25,913] INFO in player: ========== ACTION ID: t4fslOCE8y ==========
[2025-06-26 14:14:25,913] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:25,913] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-06-26 14:14:25,913] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:14:25,913] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:14:25,913] INFO in database: DEBUG: suite_id: unknown, test_idx: 0, step_idx: 6
[2025-06-26 14:14:25,914] INFO in database: DEBUG: filename: unknown, action_type: info
[2025-06-26 14:14:25,914] INFO in database: DEBUG: status: running, retry: 0/0, in_progress: True
[2025-06-26 14:14:25,914] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py", line 1300, in execute_action
    track_test_execution(

[2025-06-26 14:14:25,914] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:14:25,914] INFO in database: DEBUG: action_params: {"action_id": "t4fslOCE8y", "text": "This is just a test global[Test Case Delay]", "timestamp": 1750...
[2025-06-26 14:14:25,915] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (unknown, 0, 6, unknown)
[2025-06-26 14:14:25,915] INFO in database: DEBUG: Existing entry found: False
[2025-06-26 14:14:25,915] INFO in database: Created execution tracking for test unknown (idx: 0, step: 6): status=running, retry=0/0
[2025-06-26 14:14:25,917] INFO in database: Updated step information in test_steps table: Step 6: info
[2025-06-26 14:14:25,918] INFO in player: Tracked execution in database: test_idx=0, step_idx=6, action_type=info, action_id=t4fslOCE8y
[2025-06-26 14:14:25,918] INFO in player: Skipping device connection verification for better performance
[2025-06-26 14:14:25,918] ERROR in player: Unknown action type: info
[2025-06-26 14:14:26,559] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/latest.png
[2025-06-26 14:14:26,559] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-06-26 14:14:26,559] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_00008120_00186C801E13C01E_latest.png
[2025-06-26 14:14:26,560] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:26] "GET /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750911152934_zkxb44uh3_1750910381540_mry3dvyxe&t=1750911265408 HTTP/1.1" 200 -
[2025-06-26 14:14:26,924] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots
[2025-06-26 14:14:26,924] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-06-26 14:14:26,924] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/placeholder.png (save_debug=False)
[2025-06-26 14:14:26,924] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 14:14:28,080] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_141300/screenshots/latest.png
[2025-06-26 14:14:28,080] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-06-26 14:14:28,081] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-06-26 14:14:28,083] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 14:14:28,083] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 14:14:28,083] INFO in database: DEBUG: suite_id: , test_idx: 0, step_idx: 7
[2025-06-26 14:14:28,083] INFO in database: DEBUG: filename: unknown, action_type: info
[2025-06-26 14:14:28,083] INFO in database: DEBUG: status: in_progress, retry: 0/0, in_progress: True
[2025-06-26 14:14:28,083] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/app.py", line 3577, in execute_single_action
    track_test_execution(

[2025-06-26 14:14:28,083] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 14:14:28,083] INFO in database: DEBUG: action_params: {"action_id": "t4fslOCE8y", "text": "This is just a test global[Test Case Delay]", "timestamp": 1750...
[2025-06-26 14:14:28,083] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (, 0, 7, unknown)
[2025-06-26 14:14:28,084] INFO in database: DEBUG: Existing entry found: False
[2025-06-26 14:14:28,084] INFO in database: Created execution tracking for test unknown (idx: 0, step: 7): status=in_progress, retry=0/0
[2025-06-26 14:14:28,086] INFO in database: Updated step information in test_steps table: Step 7: info
[2025-06-26 14:14:28,086] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:28] "POST /api/action/execute HTTP/1.1" 200 -
[2025-06-26 14:14:28,092] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:28] "POST /api/logs/save HTTP/1.1" 200 -
[2025-06-26 14:14:30,092] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:14:30,092] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:30] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:14:31,096] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:14:31,097] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:31] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:14:32,937] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:14:32,938] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:32] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:14:33,094] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:14:33,096] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:33] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:14:33,098] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:14:33,099] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:33] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:14:36,094] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:14:36,095] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:36] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:14:37,935] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:14:37,936] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:37] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:14:42,935] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:14:42,936] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:42] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:14:47,937] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:14:47,938] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:47] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:14:52,936] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:14:52,937] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:52] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:14:57,935] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:14:57,936] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:14:57] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:15:02,937] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:15:02,938] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:15:02] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:15:07,934] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:15:07,935] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:15:07] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:15:12,934] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:15:12,935] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:15:12] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 14:15:17,934] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 14:15:17,934] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 14:15:17] "GET /api/reports/latest HTTP/1.1" 200 -
