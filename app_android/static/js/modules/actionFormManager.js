// app/static/js/modules/actionFormManager.js

class ActionFormManager {
    constructor(app) {
        this.app = app; // Reference to the main AppiumAutomationApp instance
        this.actionTypeSelect = document.getElementById('actionType');

        // Bind methods that might be used as event handlers or need 'this' context
        this.updateActionForm = this.updateActionForm.bind(this);
        this.handleIosFunctionChange = this.handleIosFunctionChange.bind(this);
        this.handleWaitTillLocatorTypeChange = this.handleWaitTillLocatorTypeChange.bind(this);
        this.handleIfConditionTypeChange = this.handleIfConditionTypeChange.bind(this);
        this.handleIfExistsLocatorTypeChange = this.handleIfExistsLocatorTypeChange.bind(this);
        this.handleThenActionTypeChange = this.handleThenActionTypeChange.bind(this);
        this.handleElseActionTypeChange = this.handleElseActionTypeChange.bind(this);
        this.handleConditionalIosFunctionChange = this.handleConditionalIosFunctionChange.bind(this);
    }

    // Helper function to show a specific action form and hide others
    showForm(formId) {
        document.querySelectorAll('.action-form').forEach(form => {
            form.classList.add('d-none');
        });
        const formToShow = document.getElementById(formId);
        if (formToShow) {
            formToShow.classList.remove('d-none');
        } else {
            console.warn(`Form not found: ${formId}`);
        }
    }

    // Helper function to capitalize first letter (and handle snake_case)
    capitalizeFirstLetter(string) {
        if (!string) return '';
        if (string.includes('_')) {
            return string.split('_')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                .join('');
        }
        return string.charAt(0).toUpperCase() + string.slice(1);
    }

    updateActionForm() {
        if (!this.actionTypeSelect) return;
        const selectedType = this.actionTypeSelect.value;

        // Hide all action forms first
        document.querySelectorAll('.action-form').forEach(form => form.classList.add('d-none'));

        // Show the relevant form
        if (selectedType === 'tap') {
            this.showForm('tapActionForm');
            this.app.loadReferenceImages('tap', 'tapImageFilename');
            // Add event listener for the refresh button
            const refreshBtn = document.getElementById('refreshTapImages');
            if (refreshBtn && !refreshBtn._eventListenerAttached) {
                 refreshBtn.addEventListener('click', () => this.app.loadReferenceImages('tap', 'tapImageFilename'));
                 refreshBtn._eventListenerAttached = true;
            }
        } else if (selectedType === 'doubleTap') {
            this.showForm('doubleTapActionForm');
             this.app.loadReferenceImages('doubleTap', 'doubleTapImageFilename');
            const refreshBtn = document.getElementById('refreshDoubleTapImages');
            if (refreshBtn && !refreshBtn._eventListenerAttached) {
                refreshBtn.addEventListener('click', () => this.app.loadReferenceImages('doubleTap', 'doubleTapImageFilename'));
                refreshBtn._eventListenerAttached = true;
            }
        } else if (selectedType === 'swipe') {
            this.showForm('swipeActionForm');
        } else if (selectedType === 'text') {
            this.showForm('textActionForm');
        } else if (selectedType === 'tapAndType') {
            this.showForm('tapAndTypeActionForm');
        } else if (selectedType === 'sendKeys') {
            this.showForm('sendKeysActionForm');
        } else if (selectedType === 'key') {
            this.showForm('keyActionForm');
        } else if (selectedType === 'wait') {
            this.showForm('waitActionForm');
        } else if (selectedType === 'launchApp') {
            this.showForm('launchAppActionForm');
        } else if (selectedType === 'terminateApp') {
            this.showForm('terminateAppActionForm');
        } else if (selectedType === 'restartApp') {
            this.showForm('restartAppActionForm');
        } else if (selectedType === 'uninstallApp') {
            this.showForm('uninstallAppActionForm');
        } else if (selectedType === 'waitTill') {
            this.showForm('waitTillActionForm');
            this.handleWaitTillLocatorTypeChange(); // Initialize based on current selection
            const refreshImagesBtn = document.getElementById('refreshWaitTillImages');
            if (refreshImagesBtn && !refreshImagesBtn._eventListenerAttached) {
                 refreshImagesBtn.addEventListener('click', () => this.app.loadReferenceImages('waitTill', 'waitTillImage'));
                 refreshImagesBtn._eventListenerAttached = true;
            }
        } else if (selectedType === 'swipeTillVisible') {
            this.showForm('swipeTillVisibleActionForm');
            
            // Initialize direction settings
            const directionSelect = document.getElementById('swipeTillVisibleDirection');
            const customSettingsDiv = document.getElementById('swipeTillVisibleCustomSettings');
            
            if (directionSelect && customSettingsDiv) {
                // Add event listener to toggle custom settings based on direction
                if (!directionSelect._eventListenerAttached) {
                    directionSelect.addEventListener('change', () => {
                        customSettingsDiv.style.display = directionSelect.value === 'custom' ? 'block' : 'none';
                    });
                    directionSelect._eventListenerAttached = true;
                }
                
                // Initialize display based on current selection
                customSettingsDiv.style.display = directionSelect.value === 'custom' ? 'block' : 'none';
            }
            
            // Initialize locator type settings
            const locatorTypeSelect = document.getElementById('swipeTillVisibleLocatorType');
            const locatorValueDiv = document.getElementById('swipeTillVisibleLocatorValueDiv');
            const textDiv = document.getElementById('swipeTillVisibleTextDiv');
            
            if (locatorTypeSelect && locatorValueDiv && textDiv) {
                // Add event listener to handle locator type changes
                if (!locatorTypeSelect._eventListenerAttached) {
                    locatorTypeSelect.addEventListener('change', () => {
                        const type = locatorTypeSelect.value;
                        locatorValueDiv.style.display = type === 'text' ? 'none' : 'block';
                        textDiv.style.display = type === 'text' ? 'block' : 'none';
                    });
                    locatorTypeSelect._eventListenerAttached = true;
                }
                
                // Initialize display based on current selection
                const type = locatorTypeSelect.value;
                locatorValueDiv.style.display = type === 'text' ? 'none' : 'block';
                textDiv.style.display = type === 'text' ? 'block' : 'none';
            }
            
            // Load reference images for image comparison if needed
            const refreshImagesBtn = document.getElementById('refreshSwipeTillVisibleImages');
            if (refreshImagesBtn && !refreshImagesBtn._eventListenerAttached) {
                refreshImagesBtn.addEventListener('click', () => this.app.loadReferenceImages('swipeTillVisible', 'swipeTillVisibleReferenceImage'));
                refreshImagesBtn._eventListenerAttached = true;
            }
            
            // Load reference images initially
            this.app.loadReferenceImages('swipeTillVisible', 'swipeTillVisibleReferenceImage');
            
        } else if (selectedType === 'exists') {
            this.showForm('existsActionForm');
            // Initialize exists form with proper field switching
            this.initializeExistsForm();
        } else if (selectedType === 'clickElement') {
            this.showForm('clickElementActionForm');
        } else if (selectedType === 'doubleClick') {
            this.showForm('doubleClickActionForm');
        } else if (selectedType === 'textClear') {
            this.showForm('textClearActionForm');
            const skipConditionCheckbox = document.getElementById('textClearSkipCondition');
            const skipConditionOptions = document.getElementById('textClearSkipConditionOptions');
            if (skipConditionCheckbox && skipConditionOptions) {
                skipConditionCheckbox.addEventListener('change', () => {
                    skipConditionOptions.style.display = skipConditionCheckbox.checked ? 'block' : 'none';
                    if (skipConditionCheckbox.checked) {
                        this.app.loadReferenceImages('textClearSkip', 'textClearSkipImage');
                    }
                });
                // Initialize display
                skipConditionOptions.style.display = skipConditionCheckbox.checked ? 'block' : 'none';
                if (skipConditionCheckbox.checked) {
                     this.app.loadReferenceImages('textClearSkip', 'textClearSkipImage');
                }
                const refreshImagesBtn = document.getElementById('refreshTextClearSkipImages');
                if (refreshImagesBtn && !refreshImagesBtn._eventListenerAttached) {
                    refreshImagesBtn.addEventListener('click', () => this.app.loadReferenceImages('textClearSkip', 'textClearSkipImage'));
                    refreshImagesBtn._eventListenerAttached = true;
                }
            }
        } else if (selectedType === 'hideKeyboard') {
            this.showForm('hideKeyboardActionForm');
        } else if (selectedType === 'addLog') {
            this.showForm('addLogActionForm');
        } else if (selectedType === 'airplaneMode') {
            this.showForm('airplaneModeActionForm');
        } else if (selectedType === 'addMedia') {
            this.showForm('addMediaActionForm');
            this.app.initFileUploadHandlers(); // This method might need to stay in AppiumAutomationApp or be passed
        } else if (selectedType === 'deviceBack') {
            this.showForm('deviceBackActionForm');
        } else if (selectedType === 'getValue') {
            this.showForm('getValueActionForm');
        } else if (selectedType === 'compareValue') {
            this.showForm('compareValueActionForm');
        } else if (selectedType === 'getParam') {
            this.showForm('getParamActionForm');
        } else if (selectedType === 'setParam') {
            this.showForm('setParamActionForm');
        } else if (selectedType === 'clickImage') { // OCR based
            this.showForm('clickImageActionForm'); // This is the OCR one, not clickImageAirtest
        } else if (selectedType === 'tapOnText') { // This is the specific tap on text using OCR
            this.showForm('tapOnTextActionForm');
        } else if (selectedType === 'clickImageAirtest') {
            this.showForm('clickImageAirtestActionForm');
            this.app.loadReferenceImages('clickImageAirtest', 'airtestImageFilename');
             const refreshBtn = document.getElementById('refreshClickImageAirtestImages');
            if (refreshBtn && !refreshBtn._eventListenerAttached) {
                refreshBtn.addEventListener('click', () => this.app.loadReferenceImages('clickImageAirtest', 'airtestImageFilename'));
                refreshBtn._eventListenerAttached = true;
            }
        } else if (selectedType === 'waitImageAirtest') {
            this.showForm('waitImageAirtestActionForm');
            this.app.loadReferenceImages('waitImageAirtest', 'waitAirtestImageFilename');
            const refreshBtn = document.getElementById('refreshWaitImageAirtestImages');
            if (refreshBtn && !refreshBtn._eventListenerAttached) {
                refreshBtn.addEventListener('click', () => this.app.loadReferenceImages('waitImageAirtest', 'waitAirtestImageFilename'));
                refreshBtn._eventListenerAttached = true;
            }
        } else if (selectedType === 'doubleClickImageAirtest') {
            this.showForm('doubleClickImageAirtestActionForm');
            this.app.loadReferenceImages('doubleClickImageAirtest', 'doubleClickAirtestImageFilename');
             const refreshBtn = document.getElementById('refreshDoubleClickImageAirtestImages');
            if (refreshBtn && !refreshBtn._eventListenerAttached) {
                refreshBtn.addEventListener('click', () => this.app.loadReferenceImages('doubleClickImageAirtest', 'doubleClickAirtestImageFilename'));
                refreshBtn._eventListenerAttached = true;
            }
        } else if (selectedType === 'inputText') { // This might be a duplicate or for a specific context, ensure it points to the correct form
            this.showForm('inputTextActionForm'); // Assumes inputTextActionForm is the ID
        } else if (selectedType === 'iosFunctions') {
            this.showForm('iosFunctionsActionForm');
            // Set up event listener for iOS function dropdown
            const iosFunctionSelect = document.getElementById('iosFunction');
            if (iosFunctionSelect && !iosFunctionSelect._eventListenerAttached) {
                iosFunctionSelect.addEventListener('change', () => this.handleIosFunctionChange());
                iosFunctionSelect._eventListenerAttached = true;
            }
            this.handleIosFunctionChange(); // Initial call to set up params for default selection
        } else if (selectedType === 'ifElseSteps') {
            this.showForm('ifElseStepsActionForm');
            this.handleIfConditionTypeChange(); // Initialize based on current selections
            this.handleThenActionTypeChange();
            this.handleElseActionTypeChange();
        } else if (selectedType === 'multiStep') {
            this.showForm('multiStepActionForm');
            if (this.app.multiStepAction) { // multiStepAction might be another module on App
                this.app.multiStepAction.loadTestCases();
            }
        } else if (selectedType === 'repeatSteps') {
            this.showForm('repeatStepsForm');
            this.loadRepeatStepsTestCases();
        } else if (selectedType === 'hookAction') {
            this.showForm('hookActionForm');
             if (this.app.hookAction) { // hookAction might be another module on App
                const hookActionType = document.getElementById('hookActionType');
                if (hookActionType) hookActionType.value = '';
                const hookActionFormContainer = document.getElementById('hookActionFormContainer');
                if (hookActionFormContainer) hookActionFormContainer.innerHTML = '';
            }
        } else if (selectedType === 'reporting') {
            this.showForm('reportingActionForm');
        } else if (selectedType === 'takeScreenshot') {
            this.showForm('takeScreenshotActionForm');
        } else if (selectedType === 'cleanupSteps') {
            this.showForm('cleanupStepsForm');
            this.loadCleanupStepsTestCases();
        } else if (selectedType === 'info') {
            this.showForm('infoActionForm');
        } else if (selectedType === 'tapIfImageExists') {
            this.showForm('tapIfImageExistsActionForm');
            this.app.loadReferenceImages('tapIfImageExists', 'tapIfImageExistsFilename');
            // Add event listener for the refresh button
            const refreshBtn = document.getElementById('refreshTapIfImageExistsImages');
            if (refreshBtn && !refreshBtn._eventListenerAttached) {
                refreshBtn.addEventListener('click', () => this.app.loadReferenceImages('tapIfImageExists', 'tapIfImageExistsFilename'));
                refreshBtn._eventListenerAttached = true;
            }
            // Add event listener for environment variable checkbox
            const useEnvVarCheckbox = document.getElementById('tapIfImageExistsUseEnvVar');
            const envVarContainer = document.getElementById('tapIfImageExistsEnvVarContainer');
            if (useEnvVarCheckbox && envVarContainer && !useEnvVarCheckbox._eventListenerAttached) {
                useEnvVarCheckbox.addEventListener('change', () => {
                    if (useEnvVarCheckbox.checked) {
                        envVarContainer.classList.remove('d-none');
                    } else {
                        envVarContainer.classList.add('d-none');
                    }
                });
                useEnvVarCheckbox._eventListenerAttached = true;
            }
        }
    }

    handleIosFunctionChange() {
        const iosFunctionSelect = document.getElementById('iosFunction');
        if (!iosFunctionSelect) return;
        const selectedFunction = iosFunctionSelect.value;
        const iosFunctionParamsContainer = document.getElementById('iosFunctionParamsContainer');
        if (!iosFunctionParamsContainer) return;

        iosFunctionParamsContainer.innerHTML = ''; // Clear previous parameters

        switch (selectedFunction) {
            case 'set_clipboard':
                iosFunctionParamsContainer.innerHTML = `
                    <label for="clipboardContent" class="form-label">Content:</label>
                    <input type="text" id="clipboardContent" name="clipboardContent" class="form-control" placeholder="Text to set in clipboard">
                `;
                break;
            case 'text':
                iosFunctionParamsContainer.innerHTML = `
                    <label for="iosTextInput" class="form-label">Text to Input:</label>
                    <input type="text" id="iosTextInput" name="iosTextInput" class="form-control" placeholder="Text to input on device">
                    <div class="form-check mt-2">
                        <input type="checkbox" id="iosTextEnter" name="iosTextEnter" class="form-check-input" checked>
                        <label for="iosTextEnter" class="form-check-label">Press Enter after text input</label>
                    </div>
                `;
                break;
            case 'paste_clipboard':
                 iosFunctionParamsContainer.innerHTML = '<p class="form-text text-muted mb-0">Pastes the current clipboard content at the active cursor position.</p>';
                break;
            case 'press':
                iosFunctionParamsContainer.innerHTML = `
                    <label for="iosKeyName" class="form-label">Key Name:</label>
                    <input type="text" id="iosKeyName" name="iosKeyName" class="form-control" placeholder="e.g., home, volumeUp, delete">
                `;
                break;
            case 'alert_click':
                iosFunctionParamsContainer.innerHTML = `
                    <label for="iosAlertButton" class="form-label">Button Name:</label>
                    <input type="text" id="iosAlertButton" name="iosAlertButton" class="form-control" placeholder="e.g., OK, Cancel, Allow">
                `;
                break;
            case 'alert_wait':
                iosFunctionParamsContainer.innerHTML = `
                    <label for="iosAlertTimeout" class="form-label">Timeout (seconds):</label>
                    <input type="number" id="iosAlertTimeout" name="iosAlertTimeout" class="form-control" value="10" min="1" max="60">
                `;
                break;
             case 'push':
                iosFunctionParamsContainer.innerHTML = `
                    <div class="mb-3">
                        <label for="iosPushSourcePath" class="form-label">Source Path (on server):</label>
                        <input type="text" id="iosPushSourcePath" name="source_path" class="form-control" placeholder="e.g., /path/to/your/file.png">
                        <small class="form-text text-muted">Path to the file on the server machine.</small>
                    </div>
                    <div class="mb-3">
                        <label for="iosPushDestinationPath" class="form-label">Destination Path (on device):</label>
                        <input type="text" id="iosPushDestinationPath" name="destination_path" class="form-control" placeholder="e.g., /tmp/file.png or bundleID/Documents/file.png">
                         <small class="form-text text-muted">Where to save the file on the device. For app container, use 'bundleID/Path'.</small>
                    </div>`;
                break;
            case 'clear_app':
                iosFunctionParamsContainer.innerHTML = `
                    <label for="iosClearAppBundleID" class="form-label">App Bundle ID:</label>
                    <input type="text" id="iosClearAppBundleID" name="bundle_id" class="form-control" placeholder="e.g., com.example.myapp">
                    <small class="form-text text-muted">The bundle ID of the app whose data should be cleared.</small>`;
                break;
            default:
                iosFunctionParamsContainer.innerHTML = '<p class="form-text text-muted mb-0">No parameters required for this function, or not yet implemented in UI.</p>';
        }
    }

    handleWaitTillLocatorTypeChange() {
        const locatorTypeSelect = document.getElementById('waitTillLocatorType');
        if (!locatorTypeSelect) return;
        const locatorType = locatorTypeSelect.value;
        const imageSelectDiv = document.getElementById('waitTillImageSelect');
        const locatorValueDiv = document.getElementById('waitTillLocatorValue');

        if (locatorType === 'image') {
            if (imageSelectDiv) imageSelectDiv.classList.remove('d-none');
            if (locatorValueDiv) locatorValueDiv.classList.add('d-none');
            this.app.loadReferenceImages('waitTill', 'waitTillImage');
            const captureBtn = document.getElementById('captureWaitTillScreenImage');
            if (captureBtn) captureBtn.disabled = !this.app.isConnected;
        } else {
            if (imageSelectDiv) imageSelectDiv.classList.add('d-none');
            if (locatorValueDiv) locatorValueDiv.classList.remove('d-none');
            const locatorLabel = locatorValueDiv?.querySelector('label');
            const locatorInput = document.getElementById('waitTillLocator');
            if (locatorType === 'text') {
                if (locatorLabel) locatorLabel.textContent = 'Text to Find';
                if (locatorInput) locatorInput.placeholder = 'Enter text to find on screen';
            } else {
                if (locatorLabel) locatorLabel.textContent = 'Locator Value';
                if (locatorInput) locatorInput.placeholder = 'Enter locator value';
            }
        }
    }

    resetActionForm() {
        if (this.actionTypeSelect) {
            this.actionTypeSelect.value = 'tap'; // Default action type
        }
        // Reset specific form fields (example for tap)
        const tapX = document.getElementById('tapX');
        if (tapX) tapX.value = '0';
        const tapY = document.getElementById('tapY');
        if (tapY) tapY.value = '0';
        // ... reset other forms as needed ...
        this.updateActionForm(); // Show the default form
        if (this.app.tapFallbackManager) { // tapFallbackManager would be on the app instance
            this.app.tapFallbackManager.clearFallback();
        }
    }

    getActionDescription(actionData) {
        // Prefer external action-description.js if available and type matches
        if (window.getActionDescription &&
            ['tap', 'doubleTap', 'swipe', 'text', 'exists', 'ifElseSteps', 'multiStep', 'hookAction', 'tapOnText', 'tapAndType', 'takeScreenshot', 'swipeTillVisible', 'iosFunctions'].includes(actionData.type)) {
            return window.getActionDescription(actionData, this.app); // Pass app instance if needed by external
        }

        switch (actionData.type) {
            // Cases from original getActionDescription
            case 'tap':
                if (actionData.method === 'locator' || actionData.locator_type) {
                    return `Tap on element with ${actionData.locator_type}: ${actionData.locator_value}`;
                } else if (actionData.method === 'image' || actionData.image_filename) {
                    return `Tap on image: ${actionData.image_filename}`;
                }
                return `Tap at (${actionData.x}, ${actionData.y})`;
            case 'doubleTap':
                if (actionData.method === 'locator' || actionData.locator_type) {
                    return `Double tap on element with ${actionData.locator_type}: ${actionData.locator_value}`;
                } else if (actionData.method === 'image' || actionData.image_filename) {
                    return `Double tap on image: ${actionData.image_filename}`;
                }
                return `Double tap at (${actionData.x}, ${actionData.y})`;
            case 'swipe':
                if (actionData.vector_start && actionData.vector_end) {
                    const startX = (actionData.vector_start[0] * 100).toFixed(0);
                    const startY = (actionData.vector_start[1] * 100).toFixed(0);
                    const endX = (actionData.vector_end[0] * 100).toFixed(0);
                    const endY = (actionData.vector_end[1] * 100).toFixed(0);
                    return `Swipe from (${startX}%, ${startY}%) to (${endX}%, ${endY}%)`;
                }
                return `Swipe from (${actionData.start_x}, ${actionData.start_y}) to (${actionData.end_x}, ${actionData.end_y})`;
            case 'text':
                return `Input text: "${actionData.text}"`;
            case 'tapAndType':
                if (actionData.method === 'locator') {
                    return `Tap and Type on element with ${actionData.locator_type}: "${actionData.locator_value}" with text: "${actionData.text}"`;
                }
                return `Tap and Type at (${actionData.x}, ${actionData.y}) with text: "${actionData.text}"`;
            case 'sendKeys':
                const clearText = actionData.clear_first !== false ? " (clear first)" : "";
                return `Send keys to ${actionData.locator_type}: "${actionData.locator_value}" with text: "${actionData.text}"${clearText}`;
            case 'key':
                const keyNames = {'4': 'Back', '3': 'Home', '82': 'Menu', '19': 'Up', '20': 'Down', '21': 'Left', '22': 'Right', '66': 'Enter', '67': 'Backspace'};
                const keyName = keyNames[actionData.key_code] || actionData.key_code;
                return `Press key: ${keyName}`;
            case 'wait':
                return `Wait for ${actionData.duration} seconds`;
            case 'launchApp':
                return `Launch app: ${actionData.package_id || actionData.package || ''}`;
            case 'terminateApp':
                return `Terminate app: ${actionData.package_id || actionData.package || ''}`;
            case 'restartApp':
                return `Restart app: ${actionData.package_id || actionData.package || ''}`;
            case 'uninstallApp':
                return `Uninstall app: ${actionData.package_id || ''}`;
            case 'waitTill':
                if (actionData.locator_type === 'image') {
                    return `Wait till image "${actionData.image || actionData.locator_value}" appears (timeout: ${actionData.timeout}s, interval: ${actionData.interval}s)`;
                }
                const locatorTypeNames = {'id': 'ID', 'xpath': 'XPath', 'accessibility_id': 'Accessibility ID', 'text': 'Text', 'position': 'Position'};
                const locatorTypeName = locatorTypeNames[actionData.locator_type] || actionData.locator_type;
                return `Wait till element with ${locatorTypeName} "${actionData.locator_value}" appears (timeout: ${actionData.timeout}s)`;
            case 'exists':
                if (actionData.locator_type === 'image') {
                    return `Check if image "${actionData.locator_value}" exists on screen`;
                }
                return `Check if element with ${actionData.locator_type}="${actionData.locator_value}" exists`;
            case 'clickElement':
                return `Click element with ${actionData.locator_type} locator "${actionData.locator_value}" (timeout: ${actionData.timeout}s)`;
            case 'doubleClick': // Assuming this is coordinate based if not caught by external
                return `Double click at (${actionData.x}, ${actionData.y})`;
            case 'textClear':
                let desc = `Clear field and input text: "${actionData.text}"`;
                if(actionData.delay) desc += ` after ${actionData.delay}ms delay`;
                if(actionData.skip_if_exists_type) {
                    desc += ` (skip if ${actionData.skip_if_exists_type} "${actionData.skip_if_exists_value}" found)`;
                }
                return desc;
            case 'clickImage': // OCR based
                 return `Tap on Text (OCR): '${actionData.text_to_find}'`;
            case 'tapOnText': // Specific OCR tap on text
                let tapOnTextDesc = `Tap on text: "${actionData.text_to_find}"`;
                if (actionData.double_tap) tapOnTextDesc = `Double tap on text: "${actionData.text_to_find}"`;
                if (actionData.timeout && actionData.timeout !== 30) tapOnTextDesc += ` (timeout: ${actionData.timeout}s)`;
                return tapOnTextDesc;

            case 'clickImageAirtest':
                let airtestDesc = `Click Image (Airtest): ${actionData.image_filename}`;
                if (actionData.threshold && parseFloat(actionData.threshold) !== 0.7) airtestDesc += ` (Threshold: ${actionData.threshold})`;
                if (actionData.timeout && parseInt(actionData.timeout) !== 20) airtestDesc += ` (Timeout: ${actionData.timeout}s)`;
                return airtestDesc;
            case 'waitImageAirtest':
                let waitDesc = `Wait for Image (Airtest): ${actionData.image_filename}`;
                if (actionData.threshold && parseFloat(actionData.threshold) !== 0.7) waitDesc += ` (Threshold: ${actionData.threshold})`;
                if (actionData.timeout && parseInt(actionData.timeout) !== 30) waitDesc += ` (Timeout: ${actionData.timeout}s)`;
                return waitDesc;
            case 'doubleClickImageAirtest':
                let dblClickDesc = `Double Click Image (Airtest): ${actionData.image_filename}`;
                if (actionData.threshold && parseFloat(actionData.threshold) !== 0.7) dblClickDesc += ` (Threshold: ${actionData.threshold})`;
                if (actionData.timeout && parseInt(actionData.timeout) !== 20) dblClickDesc += ` (Timeout: ${actionData.timeout}s)`;
                return dblClickDesc;
            case 'swipeTillVisible':
                let swipeTillDesc = `Swipe ${actionData.direction || 'up'} till `;
                if (actionData.image_filename) swipeTillDesc += `image "${actionData.image_filename}" is visible`;
                else if (actionData.locator_type === 'image') swipeTillDesc += `image "${actionData.locator_value}" is visible`;
                else if (actionData.locator_type && actionData.locator_value) swipeTillDesc += `element ${actionData.locator_type}: "${actionData.locator_value}" is visible`;
                else swipeTillDesc += `element is visible`;
                if (actionData.max_swipes) swipeTillDesc += ` (max ${actionData.max_swipes} swipes)`;
                return swipeTillDesc;
            case 'hideKeyboard':
                return `Hide keyboard`;
            case 'addLog':
                return `Log: "${actionData.message}" ${actionData.take_screenshot ? '(with screenshot)' : ''}`;
            case 'airplaneMode':
                return `${actionData.enabled ? 'Enable' : 'Disable'} Airplane Mode`;
            case 'addMedia':
                const mediaPath = actionData.file_path || actionData.source_path || '';
                const mediaDestination = actionData.destination_path || 'default location';
                return `Add Media: Push ${mediaPath.split('/').pop()} to ${mediaDestination}`;
            case 'deviceBack':
                return `Press Android Device Back Button`;
            case 'getValue':
                return `Get ${actionData.attribute || 'text'} from ${actionData.locator_type}: "${actionData.locator_value}"`;
            case 'compareValue':
                return `Compare ${actionData.attribute || 'text'} from ${actionData.locator_type}: "${actionData.locator_value}" to "${actionData.expected_value}"`;
            case 'getParam':
                return `Get parameter: "${actionData.param_name}"`;
            case 'setParam':
                return `Set parameter: "${actionData.param_name}" to "${actionData.param_value}"`;
            case 'iosFunctions':
                 const funcName = actionData.function_name ? actionData.function_name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'iOS Function';
                 let paramsDesc = '';
                 if(actionData.params) {
                     try {
                        const paramsObj = typeof actionData.params === 'string' ? JSON.parse(actionData.params) : actionData.params;
                        paramsDesc = Object.entries(paramsObj).map(([key, value]) => `${key}: ${value}`).join(', ');
                        if(paramsDesc) paramsDesc = ` (${paramsDesc})`;
                     } catch (e) { /* ignore parsing error for description */ }
                 }
                return `${funcName}${paramsDesc}`;
            case 'ifElseSteps':
                 return `If (${actionData.if_condition_type}...) Then (...) Else (...)`; // Simplified
            case 'multiStep':
                 return `Run Test Case: ${actionData.test_case_filename || 'Unknown Test Case'}`;
            case 'repeatSteps':
                const repeatCount = actionData.repeat_count || 1;
                const testCaseName = actionData.test_case_id || 'Unknown Test Case';
                const delay = actionData.repeat_delay ? ` (${actionData.repeat_delay}s delay)` : '';
                return `Repeat ${repeatCount}x: ${testCaseName}${delay}`;
            case 'hookAction':
                 return `Hook Action: ${actionData.hook_action_type || 'Unknown hook'}`;
             case 'reporting':
                return `Report Step: "${actionData.step_name || 'Unnamed Step'}" (Severity: ${actionData.severity || 'normal'})`;
            case 'takeScreenshot':
                return `Take Screenshot: "${actionData.screenshot_name || 'unnamed'}"`;

            default:
                return `${actionData.type ? actionData.type.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()) : 'Unknown'} action`;
        }
    }

    handleIfConditionTypeChange() {
        const conditionTypeSelect = document.getElementById('ifConditionType');
        if(!conditionTypeSelect) return;
        const conditionType = conditionTypeSelect.value || 'exists';

        document.querySelectorAll('[id^="if"][id$="ConditionForm"]').forEach(el => {
            el.style.display = 'none';
        });

        const selectedForm = document.getElementById(`if${this.capitalizeFirstLetter(conditionType)}ConditionForm`);
        if (selectedForm) {
            selectedForm.style.display = 'block';
        }

        switch (conditionType) {
            case 'exists':
            case 'not_exists': // Added not_exists
                this.handleIfExistsLocatorTypeChange(); // This needs to be adaptable or duplicated for not_exists if UI differs
                break;
            case 'screen_contains':
                this.app.loadReferenceImages('ifScreenContains', 'ifScreenContainsImage');
                const refreshBtn = document.getElementById('refreshScreenContainsImages');
                if (refreshBtn && !refreshBtn._eventListenerAttached) {
                     refreshBtn.addEventListener('click', () => this.app.loadReferenceImages('ifScreenContains', 'ifScreenContainsImage'));
                     refreshBtn._eventListenerAttached = true;
                }
                break;
        }
    }

    handleIfExistsLocatorTypeChange() {
        const locatorTypeSelect = document.getElementById('ifExistsLocatorType');
        if(!locatorTypeSelect) return;
        const locatorType = locatorTypeSelect.value;
        const locatorValueContainer = document.getElementById('ifExistsLocatorValueContainer');
        const imageContainer = document.getElementById('ifExistsImageContainer');

        if (locatorType === 'image') {
            if(locatorValueContainer) locatorValueContainer.style.display = 'none';
            if(imageContainer) imageContainer.style.display = 'block';
            this.app.loadReferenceImages('ifExists', 'ifExistsImage');
        } else {
            if(locatorValueContainer) locatorValueContainer.style.display = 'block';
            if(imageContainer) imageContainer.style.display = 'none';
        }
    }

    _createActionSpecificForm(actionType, container, prefix) {
        // Clear previous form content
        container.innerHTML = '';
        let created = true;

        switch (actionType) {
            case 'tap':
                this.createTapActionForm(container, prefix);
                break;
            case 'doubleTap':
                this.createDoubleTapActionForm(container, prefix);
                break;
            case 'tapImage': // This is likely an Airtest or similar image tap
                this.createTapImageActionForm(container, prefix); // Assuming this is the correct one
                break;
            case 'swipe':
                this.createSwipeActionForm(container, prefix);
                break;
            case 'text':
                this.createTextActionForm(container, prefix);
                break;
            case 'sendKeys':
                this.createSendKeysActionForm(container, prefix);
                break;
            case 'key':
                this.createKeyActionForm(container, prefix);
                break;
            case 'wait':
                this.createWaitActionForm(container, prefix);
                break;
            case 'hideKeyboard':
                container.innerHTML = '<div class="alert alert-info"><i class="bi bi-info-circle"></i> This action will hide the keyboard. No parameters required.</div>';
                break;
            case 'clickElement':
                this.createClickElementActionForm(container, prefix);
                break;
            case 'waitTill':
                this.createWaitTillActionForm(container, prefix);
                break;
            case 'swipeTillVisible':
                this.createSwipeTillVisibleActionForm(container, prefix);
                break;
            case 'textClear':
                this.createTextClearActionForm(container, prefix);
                break;
            case 'iosFunctions':
                this.createIosFunctionsActionForm(container, prefix);
                break;
            case 'androidFunctions':
                this.createAndroidFunctionsActionForm(container, prefix);
                break;
            case 'multiStep':
                this.createMultiStepActionForm(container, prefix);
                break;
            default:
                container.innerHTML = `<div class="alert alert-warning"><i class="bi bi-exclamation-triangle"></i> Action type "${actionType}" form not implemented for this context.</div>`;
                created = false;
        }
        return created;
    }

    handleThenActionTypeChange() {
        const actionTypeSelect = document.getElementById('thenActionType');
        const container = document.getElementById('thenActionFormContainer');
        if (!actionTypeSelect || !container) return;
        const actionType = actionTypeSelect.value;

        if (!actionType) {
            container.innerHTML = '<div class="alert alert-info"><i class="bi bi-info-circle"></i> No action will be taken if the condition is true.</div>';
            return;
        }
        this._createActionSpecificForm(actionType, container, 'then');
    }

    handleElseActionTypeChange() {
        const actionTypeSelect = document.getElementById('elseActionType');
        const container = document.getElementById('elseActionFormContainer');
         if (!actionTypeSelect || !container) return;
        const actionType = actionTypeSelect.value;

        if (!actionType) {
            container.innerHTML = '<div class="alert alert-info"><i class="bi bi-info-circle"></i> No action will be taken if the condition is false.</div>';
            return;
        }
        this._createActionSpecificForm(actionType, container, 'else');
    }
    
    handleConditionalIosFunctionChange(prefix = 'then') {
        const iosFunctionSelect = document.getElementById(`${prefix}IosFunction`);
        if (!iosFunctionSelect) return;
        const selectedFunction = iosFunctionSelect.value;
        const paramsContainer = document.getElementById(`${prefix}IosFunctionParamsContainer`);
        if (!paramsContainer) return;

        paramsContainer.innerHTML = ''; // Clear previous params

        switch (selectedFunction) {
            case 'set_clipboard':
                paramsContainer.innerHTML = `<label for="${prefix}ClipboardContent" class="form-label">Content:</label><input type="text" id="${prefix}ClipboardContent" class="form-control" placeholder="Text to set">`;
                break;
            case 'text': // Matches the main iosFunctions text input
                 paramsContainer.innerHTML = `
                    <label for="${prefix}IosTextInput" class="form-label">Text to Input:</label>
                    <input type="text" id="${prefix}IosTextInput" class="form-control" placeholder="Text to input on device">
                    <div class="form-check mt-2">
                        <input type="checkbox" id="${prefix}IosTextEnter" class="form-check-input" checked>
                        <label for="${prefix}IosTextEnter" class="form-check-label">Press Enter after text input</label>
                    </div>`;
                break;
            case 'press':
                paramsContainer.innerHTML = `<label for="${prefix}IosKeyName" class="form-label">Key Name:</label><input type="text" id="${prefix}IosKeyName" class="form-control" placeholder="e.g., home, delete">`;
                break;
            case 'alert_click':
                paramsContainer.innerHTML = `<label for="${prefix}IosAlertButton" class="form-label">Button Name:</label><input type="text" id="${prefix}IosAlertButton" class="form-control" placeholder="e.g., OK, Allow">`;
                break;
            case 'alert_wait':
                 paramsContainer.innerHTML = `<label for="${prefix}IosAlertTimeout" class="form-label">Timeout (sec):</label><input type="number" id="${prefix}IosAlertTimeout" class="form-control" value="10" min="1">`;
                break;
            // Add more cases as your main iosFunctions form supports
            default:
                paramsContainer.innerHTML = '<small class="text-muted">No parameters for this function or UI not implemented.</small>';
        }
    }


    // ---- Methods to create specific action forms ----
    // These methods will populate the passed 'container' with the form HTML
    // The 'prefix' is used for 'ifElseSteps' to differentiate between 'then' and 'else' form elements

    createTapActionForm(container, prefix = '') {
        const fullPrefix = prefix ? `${prefix}-` : '';
        container.innerHTML = `
            <ul class="nav nav-tabs mb-3" id="${fullPrefix}tapActionTabs" role="tablist">
                <li class="nav-item" role="presentation"><button class="nav-link active" id="${fullPrefix}tap-coordinates-tab" data-bs-toggle="tab" data-bs-target="#${fullPrefix}tap-coordinates" type="button">Coordinates</button></li>
                <li class="nav-item" role="presentation"><button class="nav-link" id="${fullPrefix}tap-image-tab" data-bs-toggle="tab" data-bs-target="#${fullPrefix}tap-image" type="button">Image</button></li>
                <li class="nav-item" role="presentation"><button class="nav-link" id="${fullPrefix}tap-locator-tab" data-bs-toggle="tab" data-bs-target="#${fullPrefix}tap-locator" type="button">Locator</button></li>
                <li class="nav-item" role="presentation"><button class="nav-link" id="${fullPrefix}tap-text-tab" data-bs-toggle="tab" data-bs-target="#${fullPrefix}tap-text" type="button">Text (OCR)</button></li>
            </ul>
            <div class="tab-content" id="${fullPrefix}tapActionTabContent">
                <div class="tab-pane fade show active" id="${fullPrefix}tap-coordinates" role="tabpanel">
                    <div class="row"><div class="col-6 form-group"><label>X</label><input type="number" id="${fullPrefix}tapX" class="form-control" value="0"></div><div class="col-6 form-group"><label>Y</label><input type="number" id="${fullPrefix}tapY" class="form-control" value="0"></div></div>
                    <button type="button" class="btn btn-outline-primary btn-sm mt-2" id="${fullPrefix}pickTapCoordinates"><i class="bi bi-cursor"></i> Pick</button>
                </div>
                <div class="tab-pane fade" id="${fullPrefix}tap-image" role="tabpanel">
                    <div class="form-group"><label class="form-label">Image:</label><div class="input-group"><select class="form-select" id="${fullPrefix}tapImageFilename"><option value="">Select...</option></select><button class="btn btn-outline-secondary" type="button" id="${fullPrefix}refreshTapImages"><i class="bi bi-arrow-clockwise"></i></button></div></div>
                    <button class="btn btn-outline-primary btn-sm my-2" id="${fullPrefix}captureTapImage" data-requires-connection="true" disabled><i class="bi bi-camera"></i> Capture</button>
                    <div class="row"><div class="col-6 form-group"><label class="form-label">Threshold:</label><input type="number" class="form-control" id="${fullPrefix}tapThreshold" value="0.7" min="0" max="1" step="0.05"></div><div class="col-6 form-group"><label class="form-label">Timeout (s):</label><input type="number" class="form-control" id="${fullPrefix}tapTimeout" value="20" min="1"></div></div>
                </div>
                <div class="tab-pane fade" id="${fullPrefix}tap-locator" role="tabpanel">
                    <div class="form-group"><label>Type</label><select id="${fullPrefix}tapLocatorType" class="form-control"><option value="id">ID</option><option value="xpath">XPath</option><option value="accessibility_id">Accessibility ID</option><option value="text">Text</option></select></div>
                    <div class="form-group"><label>Value</label><input type="text" id="${fullPrefix}tapLocatorValue" class="form-control"></div>
                    <div class="form-group"><label>Timeout (s)</label><input type="number" id="${fullPrefix}tapLocatorTimeout" class="form-control" value="10"></div>
                </div>
                 <div class="tab-pane fade" id="${fullPrefix}tap-text" role="tabpanel">
                    <div class="form-group"><label>Text to Find</label><input type="text" id="${fullPrefix}tapOnTextToFind" class="form-control"></div>
                    <div class="form-group"><label>Timeout (s)</label><input type="number" id="${fullPrefix}tapOnTextTimeout" class="form-control" value="30"></div>
                     <div class="form-check mt-2"><input type="checkbox" id="${fullPrefix}tapOnTextDoubleTap" class="form-check-input"><label class="form-check-label" for="${fullPrefix}tapOnTextDoubleTap">Double Tap</label></div>
                </div>
            </div>`;
        this.app.loadReferenceImages('tap', `${fullPrefix}tapImageFilename`); // Load images for the primary tab
        const refreshBtn = document.getElementById(`${fullPrefix}refreshTapImages`);
        if (refreshBtn && !refreshBtn._eventListenerAttachedConditionalTap) {
            refreshBtn.addEventListener('click', () => this.app.loadReferenceImages('tap', `${fullPrefix}tapImageFilename`));
            refreshBtn._eventListenerAttachedConditionalTap = true;
        }
         // Add listeners for pick buttons if app instance is available and methods exist
        const pickTapBtn = document.getElementById(`${fullPrefix}pickTapCoordinates`);
        if (pickTapBtn && this.app && typeof this.app.enablePickMode === 'function') {
            pickTapBtn.addEventListener('click', () => this.app.enablePickMode('tap', `${fullPrefix}tapX`, `${fullPrefix}tapY`));
        }
        const captureTapImageBtn = document.getElementById(`${fullPrefix}captureTapImage`);
         if (captureTapImageBtn && this.app && typeof this.app.startImageCapture === 'function') {
            captureTapImageBtn.addEventListener('click', () => this.app.startImageCapture('tap', `${fullPrefix}tapImageFilename`));
        }
    }

    createDoubleTapActionForm(container, prefix = '') {
        const fullPrefix = prefix ? `${prefix}-` : '';
         // Similar structure to createTapActionForm but for double tap
        container.innerHTML = `
             <ul class="nav nav-tabs mb-3" id="${fullPrefix}doubleTapActionTabs" role="tablist">
                <li class="nav-item" role="presentation"><button class="nav-link active" id="${fullPrefix}doubletap-coordinates-tab" data-bs-toggle="tab" data-bs-target="#${fullPrefix}doubletap-coordinates" type="button">Coordinates</button></li>
                <li class="nav-item" role="presentation"><button class="nav-link" id="${fullPrefix}doubletap-image-tab" data-bs-toggle="tab" data-bs-target="#${fullPrefix}doubletap-image" type="button">Image</button></li>
                <li class="nav-item" role="presentation"><button class="nav-link" id="${fullPrefix}doubletap-locator-tab" data-bs-toggle="tab" data-bs-target="#${fullPrefix}doubletap-locator" type="button">Locator</button></li>
            </ul>
            <div class="tab-content" id="${fullPrefix}doubleTapActionTabContent">
                <div class="tab-pane fade show active" id="${fullPrefix}doubletap-coordinates" role="tabpanel">
                    <div class="row"><div class="col-6 form-group"><label>X</label><input type="number" id="${fullPrefix}doubleTapX" class="form-control" value="0"></div><div class="col-6 form-group"><label>Y</label><input type="number" id="${fullPrefix}doubleTapY" class="form-control" value="0"></div></div>
                     <button type="button" class="btn btn-outline-primary btn-sm mt-2" id="${fullPrefix}pickDoubleTapCoordinates"><i class="bi bi-cursor"></i> Pick</button>
                </div>
                <div class="tab-pane fade" id="${fullPrefix}doubletap-image" role="tabpanel">
                    <div class="form-group"><label class="form-label">Image:</label><div class="input-group"><select class="form-select" id="${fullPrefix}doubleTapImageFilename"><option value="">Select...</option></select><button class="btn btn-outline-secondary" type="button" id="${fullPrefix}refreshDoubleTapImages"><i class="bi bi-arrow-clockwise"></i></button></div></div>
                     <button class="btn btn-outline-primary btn-sm my-2" id="${fullPrefix}captureDoubleTapImage" data-requires-connection="true" disabled><i class="bi bi-camera"></i> Capture</button>
                    <div class="row"><div class="col-6 form-group"><label class="form-label">Threshold:</label><input type="number" class="form-control" id="${fullPrefix}doubleTapThreshold" value="0.7" min="0" max="1" step="0.05"></div><div class="col-6 form-group"><label class="form-label">Timeout (s):</label><input type="number" class="form-control" id="${fullPrefix}doubleTapTimeout" value="20" min="1"></div></div>
                </div>
                <div class="tab-pane fade" id="${fullPrefix}doubletap-locator" role="tabpanel">
                    <div class="form-group"><label>Type</label><select id="${fullPrefix}doubleTapLocatorType" class="form-control"><option value="id">ID</option><option value="xpath">XPath</option><option value="accessibility_id">Accessibility ID</option></select></div>
                    <div class="form-group"><label>Value</label><input type="text" id="${fullPrefix}doubleTapLocatorValue" class="form-control"></div>
                    <div class="form-group"><label>Timeout (s)</label><input type="number" id="${fullPrefix}doubleTapLocatorTimeout" class="form-control" value="10"></div>
                </div>
            </div>`;
        this.app.loadReferenceImages('doubleTap', `${fullPrefix}doubleTapImageFilename`);
        const refreshBtn = document.getElementById(`${fullPrefix}refreshDoubleTapImages`);
        if (refreshBtn && !refreshBtn._eventListenerAttachedConditionalDoubleTap) {
            refreshBtn.addEventListener('click', () => this.app.loadReferenceImages('doubleTap', `${fullPrefix}doubleTapImageFilename`));
            refreshBtn._eventListenerAttachedConditionalDoubleTap = true;
        }
        const pickDoubleTapBtn = document.getElementById(`${fullPrefix}pickDoubleTapCoordinates`);
        if (pickDoubleTapBtn && this.app && typeof this.app.enablePickMode === 'function') {
            pickDoubleTapBtn.addEventListener('click', () => this.app.enablePickMode('doubleTap', `${fullPrefix}doubleTapX`, `${fullPrefix}doubleTapY`));
        }
        const captureDoubleTapImageBtn = document.getElementById(`${fullPrefix}captureDoubleTapImage`);
         if (captureDoubleTapImageBtn && this.app && typeof this.app.startImageCapture === 'function') {
            captureDoubleTapImageBtn.addEventListener('click', () => this.app.startImageCapture('doubleTap', `${fullPrefix}doubleTapImageFilename`));
        }
    }

    createSwipeActionForm(container, prefix = '') {
        const fullPrefix = prefix ? `${prefix}-` : '';
        container.innerHTML = `
            <div class="form-group mb-3"><label>Direction</label><select id="${fullPrefix}swipeDirection" class="form-control"><option value="up">Up</option><option value="down">Down</option><option value="left">Left</option><option value="right">Right</option><option value="custom">Custom</option></select></div>
            <div id="${fullPrefix}swipeCustomSettings" class="d-none"> <!-- Initially hidden for non-custom -->
                <div class="row mb-2"><div class="col-md-6"><label>Start Point (%)</label><div class="row g-1"><div class="col-6"><label for="${fullPrefix}swipeStartX" class="form-label small">X: <span id="${fullPrefix}swipeStartXValue">50</span>%</label><input type="range" id="${fullPrefix}swipeStartX" class="form-range" value="50" oninput="document.getElementById('${fullPrefix}swipeStartXValue').textContent=this.value; document.getElementById('${fullPrefix}swipeStartXInput').value=this.value;"><input type="number" id="${fullPrefix}swipeStartXInput" class="form-control form-control-sm" value="50" oninput="document.getElementById('${fullPrefix}swipeStartX').value=this.value; document.getElementById('${fullPrefix}swipeStartXValue').textContent=this.value;"></div><div class="col-6"><label for="${fullPrefix}swipeStartY" class="form-label small">Y: <span id="${fullPrefix}swipeStartYValue">50</span>%</label><input type="range" id="${fullPrefix}swipeStartY" class="form-range" value="50" oninput="document.getElementById('${fullPrefix}swipeStartYValue').textContent=this.value; document.getElementById('${fullPrefix}swipeStartYInput').value=this.value;"><input type="number" id="${fullPrefix}swipeStartYInput" class="form-control form-control-sm" value="50" oninput="document.getElementById('${fullPrefix}swipeStartY').value=this.value; document.getElementById('${fullPrefix}swipeStartYValue').textContent=this.value;"></div></div></div><div class="col-md-6"><label>End Point (%)</label><div class="row g-1"><div class="col-6"><label for="${fullPrefix}swipeEndX" class="form-label small">X: <span id="${fullPrefix}swipeEndXValue">50</span>%</label><input type="range" id="${fullPrefix}swipeEndX" class="form-range" value="50" oninput="document.getElementById('${fullPrefix}swipeEndXValue').textContent=this.value; document.getElementById('${fullPrefix}swipeEndXInput').value=this.value;"><input type="number" id="${fullPrefix}swipeEndXInput" class="form-control form-control-sm" value="50" oninput="document.getElementById('${fullPrefix}swipeEndX').value=this.value; document.getElementById('${fullPrefix}swipeEndXValue').textContent=this.value;"></div><div class="col-6"><label for="${fullPrefix}swipeEndY" class="form-label small">Y: <span id="${fullPrefix}swipeEndYValue">50</span>%</label><input type="range" id="${fullPrefix}swipeEndY" class="form-range" value="50" oninput="document.getElementById('${fullPrefix}swipeEndYValue').textContent=this.value; document.getElementById('${fullPrefix}swipeEndYInput').value=this.value;"><input type="number" id="${fullPrefix}swipeEndYInput" class="form-control form-control-sm" value="50" oninput="document.getElementById('${fullPrefix}swipeEndY').value=this.value; document.getElementById('${fullPrefix}swipeEndYValue').textContent=this.value;"></div></div></div></div>
                <button type="button" class="btn btn-outline-primary btn-sm my-2 d-none" id="${fullPrefix}pickSwipeCoordinates"><i class="bi bi-cursor"></i> Draw Swipe</button>
            </div>
            <div class="row"><div class="col-md-4 form-group"><label>Speed (ms)</label><input type="number" id="${fullPrefix}swipeDuration" class="form-control" value="300" min="50"></div><div class="col-md-4 form-group"><label>Swipes</label><input type="number" id="${fullPrefix}swipeCount" class="form-control" value="1" min="1"></div><div class="col-md-4 form-group"><label>Interval (s)</label><input type="number" id="${fullPrefix}swipeInterval" class="form-control" value="0.5" min="0.1" step="0.1"></div></div>`;
        
        const directionSelect = document.getElementById(`${fullPrefix}swipeDirection`);
        const customSettingsDiv = document.getElementById(`${fullPrefix}swipeCustomSettings`);
        const pickButton = document.getElementById(`${fullPrefix}pickSwipeCoordinates`);

        function toggleCustomSwipe() {
            if (directionSelect.value === 'custom') {
                customSettingsDiv.classList.remove('d-none');
                pickButton.classList.remove('d-none');
            } else {
                customSettingsDiv.classList.add('d-none');
                pickButton.classList.add('d-none');
            }
        }
        if(directionSelect) {
            directionSelect.addEventListener('change', toggleCustomSwipe);
            toggleCustomSwipe(); // Initial call
        }
        if (pickButton && this.app && typeof this.app.enablePickMode === 'function') {
            pickButton.addEventListener('click', () => this.app.enablePickMode('swipe', `${fullPrefix}swipeStartXInput`, `${fullPrefix}swipeStartYInput`, `${fullPrefix}swipeEndXInput`, `${fullPrefix}swipeEndYInput`));
        }
    }

    createTextActionForm(container, prefix = '') {
        const fullPrefix = prefix ? `${prefix}-` : '';
        container.innerHTML = `
            <div class="form-group mb-3">
                <label>Text</label>
                <div class="input-group">
                    <input type="text" id="${fullPrefix}inputText" class="form-control" placeholder="Enter text to input">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown"><i class="bi bi-magic"></i></button>
                    <ul class="dropdown-menu dropdown-menu-end" id="${fullPrefix}textGeneratorDropdown">
                         <li><h6 class="dropdown-header">Generate Random Data</h6></li>
                         <li><a class="dropdown-item" href="#" data-generator-id="none">No Generator</a></li>
                         <!-- Populated by JS -->
                    </ul>
                </div>
                <div class="form-text mt-1" id="${fullPrefix}textGeneratorInfo"><small class="text-muted">Use data generator for dynamic text.</small></div>
            </div>`;
        if (this.app && this.app.randomDataGenerator) {
            this.app.randomDataGenerator.populateDropdown(document.getElementById(`${fullPrefix}textGeneratorDropdown`), document.getElementById(`${fullPrefix}inputText`), document.getElementById(`${fullPrefix}textGeneratorInfo`));
        }
    }

    createSendKeysActionForm(container, prefix = '') {
         const fullPrefix = prefix ? `${prefix}-` : '';
        container.innerHTML = `
            <div class="form-group"><label>Locator Type</label><select id="${fullPrefix}sendKeysLocatorType" class="form-control"><option value="id">ID</option><option value="xpath">XPath</option><option value="accessibility_id">Accessibility ID</option><option value="class_name">Class Name</option><option value="name">Name</option><option value="css">CSS Selector</option></select></div>
            <div class="form-group"><label>Locator Value</label><input type="text" id="${fullPrefix}sendKeysLocatorValue" class="form-control"></div>
            <div class="form-group"><label>Text to Send</label>
                <div class="input-group">
                    <input type="text" id="${fullPrefix}sendKeysText" class="form-control">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown"><i class="bi bi-magic"></i></button>
                    <ul class="dropdown-menu dropdown-menu-end" id="${fullPrefix}sendKeysGeneratorDropdown">
                        <li><h6 class="dropdown-header">Generate Random Data</h6></li>
                        <li><a class="dropdown-item" href="#" data-generator-id="none">No Generator</a></li>
                    </ul>
                </div>
                 <div class="form-text mt-1" id="${fullPrefix}sendKeysGeneratorInfo"><small class="text-muted">Select a data generator.</small></div>
            </div>
            <div class="form-check mt-2"><input type="checkbox" id="${fullPrefix}sendKeysClearFirst" class="form-check-input" checked><label class="form-check-label" for="${fullPrefix}sendKeysClearFirst">Clear field first</label></div>
            <div class="form-group"><label>Timeout (s)</label><input type="number" id="${fullPrefix}sendKeysTimeout" class="form-control" value="15"></div>`;
        if (this.app && this.app.randomDataGenerator) {
            this.app.randomDataGenerator.populateDropdown(document.getElementById(`${fullPrefix}sendKeysGeneratorDropdown`), document.getElementById(`${fullPrefix}sendKeysText`), document.getElementById(`${fullPrefix}sendKeysGeneratorInfo`));
        }
    }
    
    createKeyActionForm(container, prefix = '') {
        const fullPrefix = prefix ? `${prefix}-` : '';
        container.innerHTML = `
            <div class="form-group mb-3">
                <label>Key to Press</label>
                <select id="${fullPrefix}keyCode" class="form-control">
                    <option value="4">Back (Android)</option><option value="3">Home</option><option value="82">Menu</option>
                    <option value="19">Up</option><option value="20">Down</option><option value="21">Left</option>
                    <option value="22">Right</option><option value="66">Enter</option><option value="67">Backspace</option>
                </select>
            </div>`;
    }

    createWaitActionForm(container, prefix = '') {
        const fullPrefix = prefix ? `${prefix}-` : '';
        container.innerHTML = `<div class="form-group mb-3"><label>Wait time (seconds)</label><input type="number" id="${fullPrefix}waitTime" class="form-control" value="1" min="0.1" step="0.1"></div>`;
    }

    createClickElementActionForm(container, prefix = '') {
        const fullPrefix = prefix ? `${prefix}-` : '';
        container.innerHTML = `
            <div class="form-group"><label>Locator Type</label><select id="${fullPrefix}clickElementLocatorType" class="form-control"><option value="id">ID</option><option value="xpath">XPath</option><option value="accessibility_id">Accessibility ID</option><option value="class_name">Class Name</option><option value="name">Name</option><option value="text">Text</option></select></div>
            <div class="form-group"><label>Locator Value</label><input type="text" id="${fullPrefix}clickElementLocator" class="form-control"></div>
            <div class="form-group"><label>Timeout (s)</label><input type="number" id="${fullPrefix}clickElementTimeout" class="form-control" value="10"></div>`;
    }

    createWaitTillActionForm(container, prefix = '') {
        const fullPrefix = prefix ? `${prefix}-` : '';
        container.innerHTML = `
            <div class="form-group mb-3"><label>Locator Type</label><select id="${fullPrefix}waitTillLocatorType" class="form-control"><option value="id">ID</option><option value="xpath">XPath</option><option value="accessibility_id">Accessibility ID</option><option value="text">Text</option><option value="image">Image</option></select></div>
            <div id="${fullPrefix}waitTillImageSelect" class="form-group mb-3 d-none"><label>Image</label><div class="input-group"><select id="${fullPrefix}waitTillImage" class="form-select"><option value="">Select...</option></select><button id="${fullPrefix}refreshWaitTillImages" class="btn btn-outline-secondary" type="button"><i class="bi bi-arrow-clockwise"></i></button></div><button id="${fullPrefix}captureWaitTillScreenImage" class="btn btn-sm btn-outline-primary mt-1" data-requires-connection="true" disabled><i class="bi bi-camera"></i> Capture</button></div>
            <div id="${fullPrefix}waitTillLocatorValue" class="form-group mb-3"><label>Value</label><input type="text" id="${fullPrefix}waitTillLocator" class="form-control"></div>
            <div class="row"><div class="col-6 form-group"><label>Timeout (s)</label><input type="number" id="${fullPrefix}waitTillTimeout" class="form-control" value="10"></div><div class="col-6 form-group"><label>Interval (s)</label><input type="number" id="${fullPrefix}waitTillInterval" class="form-control" value="0.5" step="0.1"></div></div>`;
        
        const locatorTypeSelect = document.getElementById(`${fullPrefix}waitTillLocatorType`);
        const imageSelectDiv = document.getElementById(`${fullPrefix}waitTillImageSelect`);
        const locatorValueDiv = document.getElementById(`${fullPrefix}waitTillLocatorValue`);
        const captureBtn = document.getElementById(`${fullPrefix}captureWaitTillScreenImage`);
        const refreshBtn = document.getElementById(`${fullPrefix}refreshWaitTillImages`);

        const updateWaitTillForm = () => {
            const type = locatorTypeSelect.value;
            if (type === 'image') {
                imageSelectDiv.classList.remove('d-none');
                locatorValueDiv.classList.add('d-none');
                this.app.loadReferenceImages('waitTill', `${fullPrefix}waitTillImage`);
                if(captureBtn) captureBtn.disabled = !this.app.isConnected;
            } else {
                imageSelectDiv.classList.add('d-none');
                locatorValueDiv.classList.remove('d-none');
                 const label = locatorValueDiv.querySelector('label');
                 const input = locatorValueDiv.querySelector('input');
                 if (type === 'text') {
                    if(label) label.textContent = 'Text to Find';
                    if(input) input.placeholder = 'Enter text to find on screen';
                 } else {
                    if(label) label.textContent = 'Locator Value';
                    if(input) input.placeholder = 'Enter locator value';
                 }
            }
        };
        if(locatorTypeSelect) {
            locatorTypeSelect.addEventListener('change', updateWaitTillForm);
            updateWaitTillForm(); // Initial call
        }
        if (refreshBtn && !refreshBtn._eventListenerAttachedConditionalWaitTill) {
             refreshBtn.addEventListener('click', () => this.app.loadReferenceImages('waitTill', `${fullPrefix}waitTillImage`));
             refreshBtn._eventListenerAttachedConditionalWaitTill = true;
        }
        if (captureBtn && this.app && typeof this.app.startImageCapture === 'function') {
            captureBtn.addEventListener('click', () => this.app.startImageCapture('waitTill', `${fullPrefix}waitTillImage`));
        }
    }

    createSwipeTillVisibleActionForm(container, prefix = '') {
        const fullPrefix = prefix ? `${prefix}-` : '';
        container.innerHTML = `
            <h6>Swipe Till Visible</h6>
            <div class="form-group mb-3"><label>Direction</label><select id="${fullPrefix}swipeTillVisibleDirection" class="form-control"><option value="up">Up</option><option value="down">Down</option><option value="left">Left</option><option value="right">Right</option><option value="custom">Custom</option></select></div>
            <div id="${fullPrefix}swipeTillVisibleCustomSettings" class="d-none"> <!-- Fields for custom swipe --> </div>
            <div class="row"><div class="col-4 form-group"><label>Duration(ms)</label><input type="number" id="${fullPrefix}swipeTillVisibleDuration" class="form-control" value="300"></div><div class="col-4 form-group"><label>Count</label><input type="number" id="${fullPrefix}swipeTillVisibleCount" class="form-control" value="1"></div><div class="col-4 form-group"><label>Interval(s)</label><input type="number" id="${fullPrefix}swipeTillVisibleInterval" class="form-control" value="0.5" step="0.1"></div></div>
            <div class="form-group mb-3"><label>Target Locator Type</label><select id="${fullPrefix}swipeTillVisibleLocatorType" class="form-control"><option value="id">ID</option><option value="xpath">XPath</option><option value="accessibility_id">Accessibility ID</option><option value="text">Text (OCR)</option><option value="image">Image</option></select></div>
            <div id="${fullPrefix}swipeTillVisibleLocatorValueDiv" class="form-group mb-3"><label>Locator Value</label><input type="text" id="${fullPrefix}swipeTillVisibleLocatorValue" class="form-control"></div>
            <div id="${fullPrefix}swipeTillVisibleTextDiv" class="form-group mb-3 d-none"><label>Text to Find</label><input type="text" id="${fullPrefix}swipeTillVisibleTextToFind" class="form-control"><small class="text-muted">Uses OCR</small></div>
            <div id="${fullPrefix}swipeTillVisibleImageDiv" class="form-group mb-3 d-none"><label>Image</label><div class="input-group"><select id="${fullPrefix}swipeTillVisibleReferenceImage" class="form-select"><option value="">Select...</option></select><button id="${fullPrefix}refreshSwipeTillVisibleImages" class="btn btn-outline-secondary" type="button"><i class="bi bi-arrow-clockwise"></i></button></div><div class="mt-1"><label>Threshold</label><input type="number" id="${fullPrefix}swipeTillVisibleThreshold" class="form-control form-control-sm" value="0.7" step="0.05" min="0" max="1"></div><div class="mt-1"><label>Timeout(s)</label><input type="number" id="${fullPrefix}swipeTillVisibleImageTimeout" class="form-control form-control-sm" value="20" min="1"></div></div>`;
        
        const directionSelect = document.getElementById(`${fullPrefix}swipeTillVisibleDirection`);
        const customSettingsDiv = document.getElementById(`${fullPrefix}swipeTillVisibleCustomSettings`);
        // Populate customSettingsDiv if needed, similar to createSwipeActionForm
        if(directionSelect && customSettingsDiv){
            directionSelect.addEventListener('change', () => {
                customSettingsDiv.classList.toggle('d-none', directionSelect.value !== 'custom');
            });
        }

        const locatorTypeSelect = document.getElementById(`${fullPrefix}swipeTillVisibleLocatorType`);
        const locatorDiv = document.getElementById(`${fullPrefix}swipeTillVisibleLocatorValueDiv`);
        const textDiv = document.getElementById(`${fullPrefix}swipeTillVisibleTextDiv`);
        const imageDiv = document.getElementById(`${fullPrefix}swipeTillVisibleImageDiv`);
        const refreshImagesBtn = document.getElementById(`${fullPrefix}refreshSwipeTillVisibleImages`);

        const updateSwipeTillForm = () => {
            const type = locatorTypeSelect.value;
            locatorDiv.classList.toggle('d-none', type === 'text' || type === 'image');
            textDiv.classList.toggle('d-none', type !== 'text');
            imageDiv.classList.toggle('d-none', type !== 'image');
            if(type === 'image') this.app.loadReferenceImages('swipeTillVisible', `${fullPrefix}swipeTillVisibleReferenceImage`);
        };
        if(locatorTypeSelect) {
            locatorTypeSelect.addEventListener('change', updateSwipeTillForm);
            updateSwipeTillForm(); // Initial call
        }
        if(refreshImagesBtn && !refreshImagesBtn._eventListenerAttachedConditionalSwipeTill) {
            refreshImagesBtn.addEventListener('click', () => this.app.loadReferenceImages('swipeTillVisible', `${fullPrefix}swipeTillVisibleReferenceImage`));
            refreshImagesBtn._eventListenerAttachedConditionalSwipeTill = true;
        }
    }

    createTextClearActionForm(container, prefix = '') {
        const fullPrefix = prefix ? `${prefix}-` : '';
        container.innerHTML = `
            <div class="form-group"><label>Text to Input</label>
                <div class="input-group">
                    <input type="text" id="${fullPrefix}textClearInput" class="form-control">
                     <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown"><i class="bi bi-magic"></i></button>
                    <ul class="dropdown-menu dropdown-menu-end" id="${fullPrefix}textClearGeneratorDropdown">
                        <li><h6 class="dropdown-header">Generate Random Data</h6></li>
                        <li><a class="dropdown-item" href="#" data-generator-id="none">No Generator</a></li>
                    </ul>
                </div>
                <div class="form-text mt-1" id="${fullPrefix}textClearGeneratorInfo"><small class="text-muted">Field cleared first.</small></div>
            </div>
            <div class="form-group mt-2"><label>Delay Before Input (ms)</label><input type="number" id="${fullPrefix}textClearDelay" class="form-control" value="500" min="0"></div>
            <div class="form-check mt-3"><input type="checkbox" id="${fullPrefix}textClearSkipCondition" class="form-check-input"><label class="form-check-label" for="${fullPrefix}textClearSkipCondition">Skip if element exists</label></div>
            <div id="${fullPrefix}textClearSkipConditionOptions" class="mt-2 d-none"> <!-- Options for skip --> </div>`;
        // Populate skip options if checkbox is checked, similar to main updateActionForm
         if (this.app && this.app.randomDataGenerator) {
            this.app.randomDataGenerator.populateDropdown(document.getElementById(`${fullPrefix}textClearGeneratorDropdown`), document.getElementById(`${fullPrefix}textClearInput`), document.getElementById(`${fullPrefix}textClearGeneratorInfo`));
        }
    }

    createTapImageActionForm(container, prefix = '') { // Assumed to be AirTest based on naming in getActionDescription
        const fullPrefix = prefix ? `${prefix}-` : '';
        container.innerHTML = `
            <div class="form-group"><label class="form-label">Reference Image:</label><div class="input-group"><select class="form-select" id="${fullPrefix}airtestImageFilename"><option value="">Select...</option></select><button class="btn btn-outline-secondary" type="button" id="${fullPrefix}refreshAirtestImages"><i class="bi bi-arrow-clockwise"></i></button></div></div>
            <button class="btn btn-outline-primary btn-sm my-2" id="${fullPrefix}captureAirtestImage" data-requires-connection="true" disabled><i class="bi bi-camera"></i> Capture</button>
            <div class="row"><div class="col-6 form-group"><label class="form-label">Threshold:</label><input type="number" class="form-control" id="${fullPrefix}airtestThreshold" value="0.7" min="0" max="1" step="0.05"></div><div class="col-6 form-group"><label class="form-label">Timeout (s):</label><input type="number" class="form-control" id="${fullPrefix}airtestTimeout" value="20" min="1"></div></div>`;
        this.app.loadReferenceImages('clickImageAirtest', `${fullPrefix}airtestImageFilename`); // Assuming 'clickImageAirtest' is the type for these images
        const refreshBtn = document.getElementById(`${fullPrefix}refreshAirtestImages`);
        if(refreshBtn && !refreshBtn._eventListenerAttachedConditionalTapImg) {
            refreshBtn.addEventListener('click', () => this.app.loadReferenceImages('clickImageAirtest', `${fullPrefix}airtestImageFilename`));
            refreshBtn._eventListenerAttachedConditionalTapImg = true;
        }
        const captureBtn = document.getElementById(`${fullPrefix}captureAirtestImage`);
        if (captureBtn && this.app && typeof this.app.startImageCapture === 'function') {
            captureBtn.addEventListener('click', () => this.app.startImageCapture('clickImageAirtest', `${fullPrefix}airtestImageFilename`));
        }
    }

    // Load test cases for repeat steps action
    async loadRepeatStepsTestCases() {
        const testCaseSelect = document.getElementById('repeatStepsTestCase');
        const refreshBtn = document.getElementById('refreshRepeatStepsTestCases');
        const testCaseInfo = document.getElementById('repeatStepsTestCaseInfo');

        if (!testCaseSelect) return;

        try {
            // Clear existing options
            testCaseSelect.innerHTML = '<option value="">-- Select Test Case --</option>';

            // Load test cases from the API using the correct endpoint
            const response = await this.app.fetchApi('recording/list', 'GET');

            if (response && response.status === 'success' && response.test_cases) {
                response.test_cases.forEach(testCase => {
                    const option = document.createElement('option');
                    option.value = testCase.filename || testCase.name;
                    option.textContent = testCase.name || testCase.filename;
                    option.dataset.testCase = JSON.stringify(testCase);
                    testCaseSelect.appendChild(option);
                });
            }

            // Add event listener for test case selection
            if (!testCaseSelect._repeatStepsEventAttached) {
                testCaseSelect.addEventListener('change', () => {
                    const selectedOption = testCaseSelect.selectedOptions[0];
                    if (selectedOption && selectedOption.dataset.testCase) {
                        const testCase = JSON.parse(selectedOption.dataset.testCase);
                        this.showRepeatStepsTestCaseInfo(testCase);
                    } else {
                        if (testCaseInfo) testCaseInfo.classList.add('d-none');
                    }
                });
                testCaseSelect._repeatStepsEventAttached = true;
            }

            // Add event listener for refresh button
            if (refreshBtn && !refreshBtn._repeatStepsEventAttached) {
                refreshBtn.addEventListener('click', () => this.loadRepeatStepsTestCases());
                refreshBtn._repeatStepsEventAttached = true;
            }

        } catch (error) {
            console.error('Error loading test cases for repeat steps:', error);
            testCaseSelect.innerHTML = '<option value="">Error loading test cases</option>';
        }
    }

    // Show test case information for repeat steps
    showRepeatStepsTestCaseInfo(testCase) {
        const testCaseInfo = document.getElementById('repeatStepsTestCaseInfo');
        const testCaseName = document.getElementById('repeatStepsTestCaseName');
        const testCaseSteps = document.getElementById('repeatStepsTestCaseSteps');
        const testCaseDescription = document.getElementById('repeatStepsTestCaseDescription');

        if (!testCaseInfo) return;

        if (testCaseName) testCaseName.textContent = testCase.name || 'N/A';
        if (testCaseSteps) testCaseSteps.textContent = testCase.actions ? testCase.actions.length : 'N/A';
        if (testCaseDescription) testCaseDescription.textContent = testCase.description || 'No description available';

        testCaseInfo.classList.remove('d-none');
    }

    createIosFunctionsActionForm(container, prefix = '') {
        const fullPrefix = prefix ? `${prefix}-` : '';
        container.innerHTML = `
            <div class="form-group mb-3">
                <label for="${fullPrefix}IosFunction" class="form-label">iOS Function</label>
                <select id="${fullPrefix}IosFunction" class="form-select">
                    <option value="home">Press Home Button</option><option value="lock">Lock Device</option><option value="unlock">Unlock Device</option>
                    <option value="press">Press Key</option><option value="alert_accept">Accept Alert</option>
                    <option value="alert_dismiss">Dismiss Alert</option><option value="alert_click">Click Alert Button</option>
                    <option value="alert_wait">Wait for Alert</option><option value="get_clipboard">Get Clipboard</option>
                    <option value="set_clipboard">Set Clipboard</option><option value="paste">Paste Clipboard</option>
                    <option value="text">Input Text (iOS specific)</option>
                    <option value="push">Push File</option><option value="clear_app">Clear App Data</option>
                    </select>
            </div>
            <div id="${fullPrefix}IosFunctionParamsContainer" class="mt-2"></div>`;
        
        const selectElement = document.getElementById(`${fullPrefix}IosFunction`);
        if(selectElement) {
            selectElement.addEventListener('change', () => this.handleConditionalIosFunctionChange(prefix));
            this.handleConditionalIosFunctionChange(prefix); // Initial call
        }
    }

    initializeExistsForm() {
        const locatorTypeSelect = document.getElementById('existsLocatorType');
        const locatorValueContainer = document.getElementById('existsLocatorValueContainer');
        const imageContainer = document.getElementById('existsImageContainer');
        const useEnvVarCheckbox = document.getElementById('existsUseEnvVar');
        const envVarContainer = document.getElementById('existsEnvVarContainer');
        const refreshButton = document.getElementById('refreshExistsImages');

        if (!locatorTypeSelect || !locatorValueContainer || !imageContainer) {
            console.warn('Exists form elements not found');
            return;
        }

        // Function to update form based on locator type
        const updateExistsForm = () => {
            const locatorType = locatorTypeSelect.value;
            const locatorValueLabel = locatorValueContainer.querySelector('label');
            const locatorValueInput = locatorValueContainer.querySelector('input');

            if (locatorType === 'image') {
                // Show image dropdown, hide text input
                locatorValueContainer.style.display = 'none';
                imageContainer.style.display = 'block';
                // Load reference images for the dropdown
                this.app.loadReferenceImages('exists', 'existsImage');
            } else if (locatorType === 'text') {
                // Show text input with appropriate label
                locatorValueContainer.style.display = 'block';
                imageContainer.style.display = 'none';
                if (locatorValueLabel) locatorValueLabel.textContent = 'Text to Find';
                if (locatorValueInput) locatorValueInput.placeholder = 'Enter text to find on screen';
            } else {
                // Show locator input for other types (id, xpath, etc.)
                locatorValueContainer.style.display = 'block';
                imageContainer.style.display = 'none';
                if (locatorValueLabel) locatorValueLabel.textContent = 'Locator Value';
                if (locatorValueInput) locatorValueInput.placeholder = 'Enter locator value';
            }
        };

        // Handle environment variable checkbox
        if (useEnvVarCheckbox && envVarContainer) {
            useEnvVarCheckbox.addEventListener('change', () => {
                if (useEnvVarCheckbox.checked) {
                    envVarContainer.classList.remove('d-none');
                } else {
                    envVarContainer.classList.add('d-none');
                }
            });
        }

        // Handle refresh button
        if (refreshButton) {
            refreshButton.addEventListener('click', () => {
                this.app.loadReferenceImages('exists', 'existsImage');
            });
        }

        // Add event listener if not already attached
        if (!locatorTypeSelect._existsEventListenerAttached) {
            locatorTypeSelect.addEventListener('change', updateExistsForm);
            locatorTypeSelect._existsEventListenerAttached = true;
        }

        // Initialize form based on current selection
        updateExistsForm();
    }

    createMultiStepActionForm(container, prefix = '') {
        const fullPrefix = prefix ? `${prefix}-` : '';
        container.innerHTML = `
            <div class="alert alert-info mb-3">
                <i class="bi bi-info-circle"></i> This action allows you to include an entire test case as a single step.
            </div>
            <div class="form-group mb-3">
                <label for="${fullPrefix}multiStepTestCase" class="form-label">Select Test Case</label>
                <div class="input-group">
                    <select id="${fullPrefix}multiStepTestCase" name="${fullPrefix}multiStepTestCase" class="form-select">
                        <option value="">-- Select Test Case --</option>
                    </select>
                    <button class="btn btn-outline-secondary" type="button" id="${fullPrefix}refreshMultiStepTestCases">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                </div>
            </div>

            <div id="${fullPrefix}multiStepTestCaseInfo" class="d-none">
                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">Test Case Details</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>Name:</strong> <span id="${fullPrefix}multiStepTestCaseName"></span></p>
                        <p><strong>Steps:</strong> <span id="${fullPrefix}multiStepTestCaseSteps"></span></p>
                        <p><strong>Description:</strong> <span id="${fullPrefix}multiStepTestCaseDescription"></span></p>

                        <!-- Loading indicator for pre-loading steps -->
                        <div id="${fullPrefix}multiStepLoadingIndicator" class="d-none mt-2">
                            <div class="d-flex align-items-center">
                                <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span>Pre-loading test case steps...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Set up event listeners for the multi-step form
        const testCaseSelect = document.getElementById(`${fullPrefix}multiStepTestCase`);
        const refreshButton = document.getElementById(`${fullPrefix}refreshMultiStepTestCases`);
        const testCaseInfo = document.getElementById(`${fullPrefix}multiStepTestCaseInfo`);

        // Load test cases initially
        this.loadMultiStepTestCases(fullPrefix);

        // Handle test case selection
        if (testCaseSelect && !testCaseSelect._multiStepEventAttached) {
            testCaseSelect.addEventListener('change', () => {
                const selectedOption = testCaseSelect.selectedOptions[0];
                if (selectedOption && selectedOption.dataset.testCase) {
                    const testCase = JSON.parse(selectedOption.dataset.testCase);
                    this.showMultiStepTestCaseInfo(testCase, fullPrefix);
                } else {
                    if (testCaseInfo) testCaseInfo.classList.add('d-none');
                }
            });
            testCaseSelect._multiStepEventAttached = true;
        }

        // Handle refresh button
        if (refreshButton && !refreshButton._multiStepRefreshAttached) {
            refreshButton.addEventListener('click', () => {
                this.loadMultiStepTestCases(fullPrefix);
            });
            refreshButton._multiStepRefreshAttached = true;
        }
    }

    loadMultiStepTestCases(prefix = '') {
        const selectId = `${prefix}multiStepTestCase`;
        const selectElement = document.getElementById(selectId);

        if (!selectElement) {
            console.error(`Test case select element not found: ${selectId}`);
            return;
        }

        // Show loading state
        selectElement.innerHTML = '<option value="">Loading test cases...</option>';
        selectElement.disabled = true;

        // Fetch test cases from the API
        fetch('/api/test_cases_for_multi_step')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.test_cases) {
                    // Clear the select and add default option
                    selectElement.innerHTML = '<option value="">-- Select Test Case --</option>';

                    // Add test cases to the dropdown
                    data.test_cases.forEach(testCase => {
                        const option = document.createElement('option');
                        option.value = testCase.id;
                        option.textContent = `${testCase.name} (${testCase.steps} steps)`;
                        option.dataset.testCase = JSON.stringify(testCase);
                        selectElement.appendChild(option);
                    });

                    console.log(`Loaded ${data.test_cases.length} test cases for ${selectId}`);
                } else {
                    selectElement.innerHTML = '<option value="">No test cases available</option>';
                    console.error('Failed to load test cases:', data.error || 'Unknown error');
                }
            })
            .catch(error => {
                console.error('Error loading test cases:', error);
                selectElement.innerHTML = '<option value="">Error loading test cases</option>';
            })
            .finally(() => {
                selectElement.disabled = false;
            });
    }

    showMultiStepTestCaseInfo(testCase, prefix = '') {
        const testCaseInfo = document.getElementById(`${prefix}multiStepTestCaseInfo`);
        const testCaseName = document.getElementById(`${prefix}multiStepTestCaseName`);
        const testCaseSteps = document.getElementById(`${prefix}multiStepTestCaseSteps`);
        const testCaseDescription = document.getElementById(`${prefix}multiStepTestCaseDescription`);

        if (testCaseInfo && testCaseName && testCaseSteps && testCaseDescription) {
            testCaseName.textContent = testCase.name || 'Unknown';
            testCaseSteps.textContent = testCase.steps ? testCase.steps.length : 0;
            testCaseDescription.textContent = testCase.description || 'No description available';
            testCaseInfo.classList.remove('d-none');
        }
    }

    loadCleanupStepsTestCases() {
        // Use the app's multiStepAction module to load test cases for cleanup steps
        if (this.app.multiStepAction) {
            this.app.multiStepAction.loadTestCases('cleanupStepsTestCase');
        }

        // Set up event listeners for cleanup steps form
        const testCaseSelect = document.getElementById('cleanupStepsTestCase');
        const refreshButton = document.getElementById('refreshCleanupStepsTestCases');
        const testCaseInfo = document.getElementById('cleanupStepsTestCaseInfo');

        // Handle test case selection
        if (testCaseSelect && !testCaseSelect._cleanupStepsEventAttached) {
            testCaseSelect.addEventListener('change', () => {
                const selectedOption = testCaseSelect.selectedOptions[0];
                if (selectedOption && selectedOption.dataset.testCase) {
                    const testCase = JSON.parse(selectedOption.dataset.testCase);
                    this.showCleanupStepsTestCaseInfo(testCase);
                } else {
                    if (testCaseInfo) testCaseInfo.classList.add('d-none');
                }
            });
            testCaseSelect._cleanupStepsEventAttached = true;
        }

        // Handle refresh button
        if (refreshButton && !refreshButton._cleanupStepsRefreshAttached) {
            refreshButton.addEventListener('click', () => {
                this.loadCleanupStepsTestCases();
            });
            refreshButton._cleanupStepsRefreshAttached = true;
        }
    }

    showCleanupStepsTestCaseInfo(testCase) {
        const testCaseInfo = document.getElementById('cleanupStepsTestCaseInfo');
        const testCaseName = document.getElementById('cleanupStepsTestCaseName');
        const testCaseSteps = document.getElementById('cleanupStepsTestCaseSteps');
        const testCaseDescription = document.getElementById('cleanupStepsTestCaseDescription');

        if (testCaseInfo && testCaseName && testCaseSteps && testCaseDescription) {
            testCaseName.textContent = testCase.name || 'Unknown';
            testCaseSteps.textContent = testCase.steps ? testCase.steps.length : 0;
            testCaseDescription.textContent = testCase.description || 'No description available';
            testCaseInfo.classList.remove('d-none');
        }
    }
}

// Make it available globally if not using modules, or export if using modules
window.ActionFormManager = ActionFormManager;