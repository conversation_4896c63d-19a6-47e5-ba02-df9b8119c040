from .base_action import BaseAction
import logging
import traceback
import json

class IfElseStepsAction(BaseAction):
    """Handler for if conditional actions"""

    def execute(self, params):
        """
        Execute a conditional action - if condition is true, execute then action

        Args:
            params: Dictionary containing:
                - condition_type: Type of condition (exists, not_exists, visible, contains_text, value_equals, has_attribute, etc.)
                - condition: Condition parameters
                - then_action: Action to execute if condition is true

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        condition_type = params.get('condition_type')
        condition = params.get('condition', {})
        then_action = params.get('then_action', {})

        # Log the conditional action
        self.logger.info(f"Executing If Steps with condition type: {condition_type}")
        self.logger.info(f"Then action: {then_action}")

        # Check condition based on type
        condition_met = False

        # Log the condition details
        self.logger.info(f"Checking condition of type: {condition_type}")
        self.logger.info(f"Condition parameters: {condition}")
        self.logger.info(f"Then action: {then_action}")

        try:
            if condition_type in ['exists', 'not_exists']:
                locator_type = condition.get('locator_type')
                locator_value = condition.get('locator_value')
                # Get timeout from global settings or use provided value
                default_timeout = self.get_global_timeout()
                timeout = condition.get('timeout', default_timeout)

                if not locator_type or not locator_value:
                    return {"status": "error", "message": "Missing locator parameters for condition"}

                # Check if element exists
                self.logger.info(f"Checking if element {condition_type == 'not_exists' and 'does not exist' or 'exists'} with {locator_type}: {locator_value}, timeout={timeout}s")

                # Use appropriate method based on locator type
                if locator_type == 'image':
                    element_found = self.controller.find_image(locator_value, timeout=timeout)
                else:
                    element_found = self.controller.find_element(locator_type, locator_value, timeout=timeout)

                # For 'exists', condition is met if element is found
                # For 'not_exists', condition is met if element is NOT found
                if condition_type == 'exists':
                    condition_met = element_found is not None
                    self.logger.info(f"Condition check result: Element exists = {condition_met}")
                else:  # not_exists
                    condition_met = element_found is None
                    self.logger.info(f"Condition check result: Element does not exist = {condition_met}")

            elif condition_type == 'visible':
                locator_type = condition.get('locator_type')
                locator_value = condition.get('locator_value')
                timeout = condition.get('timeout', 10)

                if not locator_type or not locator_value:
                    return {"status": "error", "message": "Missing locator parameters for condition"}

                # Check if element is visible
                self.logger.info(f"Checking if element is visible with {locator_type}: {locator_value}, timeout={timeout}s")

                element = self.controller.find_element(locator_type, locator_value, timeout=timeout)
                condition_met = element is not None and element.is_displayed()
                self.logger.info(f"Condition check result: Element visible = {condition_met}")

            elif condition_type == 'contains_text':
                locator_type = condition.get('locator_type')
                locator_value = condition.get('locator_value')
                text = condition.get('text')
                timeout = condition.get('timeout', 10)

                if not locator_type or not locator_value or not text:
                    return {"status": "error", "message": "Missing parameters for Contains Text condition"}

                # Check if element contains text
                self.logger.info(f"Checking if element {locator_type}: {locator_value} contains text: '{text}', timeout={timeout}s")

                element = self.controller.find_element(locator_type, locator_value, timeout=timeout)
                if element is not None:
                    element_text = element.text
                    condition_met = text in element_text
                    self.logger.info(f"Element text: '{element_text}', contains '{text}' = {condition_met}")
                else:
                    self.logger.info("Element not found for contains_text check")

            elif condition_type == 'value_equals':
                locator_type = condition.get('locator_type')
                locator_value = condition.get('locator_value')
                expected_value = condition.get('expected_value')
                timeout = condition.get('timeout', 10)

                if not locator_type or not locator_value or expected_value is None:
                    return {"status": "error", "message": "Missing parameters for Value Equals condition"}

                # Check if element value equals expected value
                self.logger.info(f"Checking if element {locator_type}: {locator_value} value equals: '{expected_value}', timeout={timeout}s")

                element = self.controller.find_element(locator_type, locator_value, timeout=timeout)
                if element is not None:
                    if element.get_attribute('value') is not None:
                        actual_value = element.get_attribute('value')
                    else:
                        actual_value = element.text

                    condition_met = actual_value == expected_value
                    self.logger.info(f"Element value: '{actual_value}', equals '{expected_value}' = {condition_met}")
                else:
                    self.logger.info("Element not found for value_equals check")

            elif condition_type == 'value_contains':
                locator_type = condition.get('locator_type')
                locator_value = condition.get('locator_value')
                expected_value = condition.get('expected_value')
                timeout = condition.get('timeout', 10)

                if not locator_type or not locator_value or not expected_value:
                    return {"status": "error", "message": "Missing parameters for Value Contains condition"}

                # Check if element value contains expected value
                self.logger.info(f"Checking if element {locator_type}: {locator_value} value contains: '{expected_value}', timeout={timeout}s")

                element = self.controller.find_element(locator_type, locator_value, timeout=timeout)
                if element is not None:
                    if element.get_attribute('value') is not None:
                        actual_value = element.get_attribute('value')
                    else:
                        actual_value = element.text

                    condition_met = expected_value in actual_value
                    self.logger.info(f"Element value: '{actual_value}', contains '{expected_value}' = {condition_met}")
                else:
                    self.logger.info("Element not found for value_contains check")

            elif condition_type == 'has_attribute':
                locator_type = condition.get('locator_type')
                locator_value = condition.get('locator_value')
                attribute_name = condition.get('attribute_name')
                expected_value = condition.get('attribute_value')
                timeout = condition.get('timeout', 10)

                if not locator_type or not locator_value or not attribute_name:
                    return {"status": "error", "message": "Missing parameters for Has Attribute condition"}

                # Check if element has the specified attribute with the expected value
                self.logger.info(f"Checking if element {locator_type}: {locator_value} has attribute '{attribute_name}'{expected_value and f' with value: {expected_value}' or ''}, timeout={timeout}s")

                element = self.controller.find_element(locator_type, locator_value, timeout=timeout)
                if element is not None:
                    try:
                        actual_value = element.get_attribute(attribute_name)
                        self.logger.info(f"Element attribute '{attribute_name}' value: '{actual_value}'")

                        # If expected_value is provided, check if it matches
                        if expected_value:
                            condition_met = str(actual_value).lower() == str(expected_value).lower()
                            self.logger.info(f"Attribute value matches expected value: {condition_met}")
                        else:
                            # If no expected_value is provided, just check if the attribute exists
                            condition_met = actual_value is not None
                            self.logger.info(f"Attribute exists: {condition_met}")
                    except Exception as e:
                        self.logger.error(f"Error getting attribute: {e}")
                        condition_met = False
                else:
                    self.logger.info("Element not found for has_attribute check")
                    condition_met = False

            elif condition_type == 'screen_contains':
                image = condition.get('image')
                threshold = condition.get('threshold', 0.7)
                timeout = condition.get('timeout', 10)

                if not image:
                    return {"status": "error", "message": "Missing image parameter for Screen Contains condition"}

                # Check if screen contains image
                self.logger.info(f"Checking if screen contains image: {image}, threshold={threshold}, timeout={timeout}s")

                match_result = self.controller.find_image(image, threshold=threshold, timeout=timeout)

                # Validate the match result to ensure it's not None and doesn't contain invalid coordinates
                if match_result is not None:
                    # Import the coordinate validator
                    try:
                        import sys
                        import os
                        current_dir = os.path.dirname(os.path.abspath(__file__))
                        parent_dir = os.path.dirname(current_dir)
                        utils_dir = os.path.join(parent_dir, 'utils')
                        if utils_dir not in sys.path:
                            sys.path.insert(0, utils_dir)
                        from coordinate_validator import validate_coordinates
                    except ImportError:
                        def validate_coordinates(coords):
                            return coords if coords else None

                    # Validate coordinates to prevent infinity or NaN values
                    valid_coords = validate_coordinates(match_result)

                    if valid_coords:
                        condition_met = True
                        self.logger.info(f"Screen contains image at valid position: {valid_coords}")
                    else:
                        condition_met = False
                        self.logger.error(f"Screen contains image but coordinates are invalid: {match_result}")
                else:
                    condition_met = False

                self.logger.info(f"Screen contains image check result = {condition_met}")

            else:
                return {"status": "error", "message": f"Unsupported condition type: {condition_type}"}

        except Exception as e:
            self.logger.error(f"Error checking condition: {e}")
            self.logger.error(traceback.format_exc())
            return {"status": "error", "message": f"Error checking condition: {str(e)}"}

        # Create a new action factory instance
        from action_factory import ActionFactory
        action_factory = ActionFactory(self.controller)

        # If condition is met, execute then_action (if provided)
        if condition_met:
            # Check if a then_action was provided
            if then_action is None or not then_action.get('type'):
                self.logger.info("Condition met, but no Then action was provided (No Action selected)")
                return {"status": "success", "message": "Condition met, no action taken (No Action selected)"}

            self.logger.info(f"Condition met, executing then action: {then_action.get('type')}")

            # Process the then action based on its type and method
            try:
                # Special handling for different action types
                if then_action.get('type') == 'tap':
                    method = then_action.get('method', 'coordinates')
                    self.logger.info(f"Then action is tap with method: {method}")

                    # Handle different tap methods
                    if method == 'coordinates':
                        # Coordinates-based tap
                        self.logger.info(f"Executing tap at coordinates: ({then_action.get('x')}, {then_action.get('y')})")
                    elif method == 'image':
                        # Image-based tap
                        self.logger.info(f"Executing tap on image: {then_action.get('image_filename')}")
                    elif method == 'locator':
                        # Locator-based tap
                        self.logger.info(f"Executing tap on locator: {then_action.get('locator_type')}={then_action.get('locator_value')}")
                    elif method == 'text':
                        # Text-based tap
                        self.logger.info(f"Executing tap on text: {then_action.get('text_to_find')}")
                        # Convert to tap_on_text action
                        tap_on_text_params = {
                            'text_to_find': then_action.get('text_to_find'),
                            'timeout': then_action.get('timeout', 30)
                        }
                        result = action_factory.execute_action('tapOnText', tap_on_text_params)
                        return {
                            "status": result.get('status', 'error'),
                            "message": f"Condition true, executed tap on text: {result.get('message', 'completed')}"
                        }
                elif then_action.get('type') == 'wait':
                    # Special handling for wait action
                    self.logger.info(f"Then action is wait with time: {then_action.get('time')}ms")

                    # Convert milliseconds to seconds for the wait action
                    wait_time_ms = then_action.get('time', 1000)
                    wait_time_sec = wait_time_ms / 1000.0

                    # Create parameters for wait action
                    wait_params = {
                        'duration': wait_time_sec
                    }

                    self.logger.info(f"Executing wait for {wait_time_sec} seconds")
                    result = action_factory.execute_action('wait', wait_params)
                    return {
                        "status": result.get('status', 'error'),
                        "message": f"Condition true, executed wait: {result.get('message', 'completed')}"
                    }

                # Execute the then action using the action factory
                self.logger.info(f"Executing then action with action factory: {then_action.get('type')}")
                self.logger.info(f"Then action parameters: {then_action}")
                result = action_factory.execute_action(then_action.get('type'), then_action)
                self.logger.info(f"Then action result: {result}")
                return {
                    "status": "success",
                    "message": f"Condition true, executed action: {result.get('message', 'completed')}"
                }
            except Exception as e:
                self.logger.error(f"Error executing then action: {e}")
                self.logger.error(traceback.format_exc())
                return {"status": "error", "message": f"Error executing then action: {str(e)}"}
        else:
            # If condition is not met, execute else action (if provided)
            else_action = params.get('else_action')
            if else_action is None or not else_action.get('type'):
                self.logger.info("Condition not met, no else action provided")
                return {"status": "success", "message": "Condition not met, no action taken"}

            self.logger.info(f"Condition not met, executing else action: {else_action.get('type')}")
            try:
                # Handle wait action specially for else action
                if else_action.get('type') == 'wait':
                    wait_time = else_action.get('time', 1)
                    wait_time_sec = float(wait_time)
                    wait_params = {'time': wait_time_sec}

                    self.logger.info(f"Executing wait for {wait_time_sec} seconds")
                    result = action_factory.execute_action('wait', wait_params)
                    return {
                        "status": result.get('status', 'error'),
                        "message": f"Condition false, executed wait: {result.get('message', 'completed')}"
                    }

                # Execute the else action using the action factory
                self.logger.info(f"Executing else action with action factory: {else_action.get('type')}")
                self.logger.info(f"Else action parameters: {else_action}")
                result = action_factory.execute_action(else_action.get('type'), else_action)
                self.logger.info(f"Else action result: {result}")
                return {
                    "status": "success",
                    "message": f"Condition false, executed action: {result.get('message', 'completed')}"
                }
            except Exception as e:
                self.logger.error(f"Error executing else action: {e}")
                self.logger.error(traceback.format_exc())
                return {"status": "error", "message": f"Error executing else action: {str(e)}"}